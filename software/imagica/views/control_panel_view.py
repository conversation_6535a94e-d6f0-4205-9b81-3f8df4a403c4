"""
控制面板视图模块

此模块负责图像处理参数的UI界面，包括：
1. 各种处理控件
2. 参数调整界面
3. 处理操作界面
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QLabel, QSlider, QCheckBox, QPushButton,
    QComboBox, QTabWidget, QScrollArea, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from controllers.ui_controller import UIController

class ControlPanelView(QScrollArea):
    """控制面板视图类，负责图像处理参数的UI界面"""
    
    def __init__(self, ui_controller: UIController, parent=None):
        """
        初始化控制面板视图
        
        Args:
            ui_controller: UI控制器
            parent: 父控件
        """
        super().__init__(parent)
        
        self.ui_controller = ui_controller
        
        # 设置滚动区域属性
        self.setWidgetResizable(True)
        self.setMinimumWidth(280)
        
        # 创建主控件
        self.control_widget = QWidget()
        self.setWidget(self.control_widget)
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self.control_widget)
        self.main_layout.setSpacing(10)
        
        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.main_layout.addWidget(self.tab_widget)
        
        # 创建各个标签页
        self._create_basic_tab()
        self._create_advanced_tab()
        self._create_color_tab()
        self._create_effects_tab()
        self._create_vlm_analysis_tab()
        
        # 创建操作按钮
        self._create_action_buttons()
    
    def _create_basic_tab(self) -> None:
        """创建基本调整标签页"""
        basic_tab = QWidget()
        layout = QVBoxLayout(basic_tab)
        layout.setSpacing(15)
        
        # 亮度控制组
        brightness_group = QGroupBox("亮度")
        brightness_layout = QVBoxLayout(brightness_group)
        
        brightness_checkbox = QCheckBox("启用亮度调整")
        brightness_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('brightness'))
        brightness_layout.addWidget(brightness_checkbox)
        
        brightness_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('brightness_strength', value)
        )
        brightness_layout.addLayout(brightness_slider)
        
        highlight_slider = self._create_slider_control(
            "高光", -50, 50, 0,
            lambda value: self.ui_controller.on_parameter_changed('brightness_highlight', value)
        )
        brightness_layout.addLayout(highlight_slider)
        
        shadow_slider = self._create_slider_control(
            "阴影", -50, 50, 0,
            lambda value: self.ui_controller.on_parameter_changed('brightness_shadow', value)
        )
        brightness_layout.addLayout(shadow_slider)
        
        layout.addWidget(brightness_group)
        
        # 对比度控制组
        contrast_group = QGroupBox("对比度")
        contrast_layout = QVBoxLayout(contrast_group)
        
        contrast_checkbox = QCheckBox("启用对比度调整")
        contrast_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('contrast'))
        contrast_layout.addWidget(contrast_checkbox)
        
        contrast_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('contrast_strength', value)
        )
        contrast_layout.addLayout(contrast_slider)
        
        bright_slider = self._create_slider_control(
            "亮部", -50, 50, 0,
            lambda value: self.ui_controller.on_parameter_changed('contrast_bright', value)
        )
        contrast_layout.addLayout(bright_slider)
        
        dark_slider = self._create_slider_control(
            "暗部", -50, 50, 0,
            lambda value: self.ui_controller.on_parameter_changed('contrast_dark', value)
        )
        contrast_layout.addLayout(dark_slider)
        
        layout.addWidget(contrast_group)
        
        # 饱和度控制组
        saturation_group = QGroupBox("饱和度")
        saturation_layout = QVBoxLayout(saturation_group)
        
        saturation_checkbox = QCheckBox("启用饱和度调整")
        saturation_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('saturation'))
        saturation_layout.addWidget(saturation_checkbox)
        
        saturation_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('saturation_strength', value)
        )
        saturation_layout.addLayout(saturation_slider)
        
        layout.addWidget(saturation_group)
        
        # 色温控制组
        temperature_group = QGroupBox("色温")
        temperature_layout = QVBoxLayout(temperature_group)
        
        temperature_checkbox = QCheckBox("启用色温调整")
        temperature_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('temperature'))
        temperature_layout.addWidget(temperature_checkbox)
        
        temperature_slider = self._create_slider_control(
            "色温", -50, 50, 0,
            lambda value: self.ui_controller.on_parameter_changed('temperature_strength', value)
        )
        temperature_layout.addLayout(temperature_slider)
        
        layout.addWidget(temperature_group)
        
        # 添加弹性空间
        layout.addStretch(1)
        
        # 添加到标签页
        self.tab_widget.addTab(basic_tab, "基本调整")
    
    def _create_advanced_tab(self) -> None:
        """创建高级调整标签页"""
        advanced_tab = QWidget()
        layout = QVBoxLayout(advanced_tab)
        layout.setSpacing(15)
        
        # 锐化控制组
        sharpen_group = QGroupBox("锐化")
        sharpen_layout = QVBoxLayout(sharpen_group)
        
        sharpen_checkbox = QCheckBox("启用锐化")
        sharpen_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('sharpen'))
        sharpen_layout.addWidget(sharpen_checkbox)
        
        sharpen_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('sharpen_strength', value)
        )
        sharpen_layout.addLayout(sharpen_slider)
        
        radius_slider = self._create_slider_control(
            "半径", 1, 5, 1,
            lambda value: self.ui_controller.on_parameter_changed('sharpen_radius', value)
        )
        sharpen_layout.addLayout(radius_slider)
        
        sharpen_mode_layout = QHBoxLayout()
        sharpen_mode_layout.addWidget(QLabel("锐化模式:"))
        
        sharpen_mode_combo = QComboBox()
        sharpen_mode_combo.addItems(["USM锐化", "自适应锐化", "边缘锐化", "AI锐化"])
        sharpen_mode_combo.currentTextChanged.connect(
            lambda text: self.ui_controller.on_parameter_changed('sharpen_mode', text)
        )
        sharpen_mode_layout.addWidget(sharpen_mode_combo)
        
        sharpen_layout.addLayout(sharpen_mode_layout)
        
        layout.addWidget(sharpen_group)
        
        # 降噪控制组
        denoise_group = QGroupBox("降噪")
        denoise_layout = QVBoxLayout(denoise_group)
        
        denoise_checkbox = QCheckBox("启用降噪")
        denoise_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('denoise'))
        denoise_layout.addWidget(denoise_checkbox)
        
        denoise_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('denoise_strength', value)
        )
        denoise_layout.addLayout(denoise_slider)
        
        denoise_type_layout = QHBoxLayout()
        denoise_type_layout.addWidget(QLabel("降噪类型:"))
        
        denoise_type_combo = QComboBox()
        denoise_type_combo.addItems(["非局部均值", "双边滤波", "高斯滤波"])
        denoise_type_combo.currentIndexChanged.connect(
            lambda index: self.ui_controller.on_parameter_changed('denoise_type', index)
        )
        denoise_type_layout.addWidget(denoise_type_combo)
        
        denoise_layout.addLayout(denoise_type_layout)
        
        layout.addWidget(denoise_group)
        
        # 超分辨率控制组
        superres_group = QGroupBox("超分辨率")
        superres_layout = QVBoxLayout(superres_group)
        
        superres_checkbox = QCheckBox("启用超分辨率")
        superres_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('superres'))
        superres_layout.addWidget(superres_checkbox)
        
        superres_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('superres_strength', value)
        )
        superres_layout.addLayout(superres_slider)
        
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("放大比例:"))
        
        scale_combo = QComboBox()
        scale_combo.addItems(["2x", "3x", "4x"])
        scale_combo.currentIndexChanged.connect(
            lambda index: self.ui_controller.on_parameter_changed('superres_scale', index + 2)
        )
        scale_layout.addWidget(scale_combo)
        
        superres_layout.addLayout(scale_layout)
        
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("质量:"))
        
        quality_combo = QComboBox()
        quality_combo.addItems(["低", "中", "高"])
        quality_combo.currentIndexChanged.connect(
            lambda index: self.ui_controller.on_parameter_changed('superres_quality', index)
        )
        quality_layout.addWidget(quality_combo)
        
        superres_layout.addLayout(quality_layout)
        
        layout.addWidget(superres_group)
        
        # 添加弹性空间
        layout.addStretch(1)
        
        # 添加到标签页
        self.tab_widget.addTab(advanced_tab, "高级调整")
    
    def _create_color_tab(self) -> None:
        """创建颜色调整标签页"""
        color_tab = QWidget()
        layout = QVBoxLayout(color_tab)
        layout.setSpacing(15)
        
        # 调色控制组
        color_group = QGroupBox("调色")
        color_layout = QVBoxLayout(color_group)
        
        color_checkbox = QCheckBox("启用调色")
        color_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('color'))
        color_layout.addWidget(color_checkbox)
        
        color_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('color_strength', value)
        )
        color_layout.addLayout(color_slider)
        
        style_layout = QHBoxLayout()
        style_layout.addWidget(QLabel("颜色风格:"))
        
        style_combo = QComboBox()
        style_combo.addItems(["自然", "鲜艳", "柔和", "复古", "黑白", "冷色调", "暖色调"])
        style_combo.currentTextChanged.connect(
            lambda text: self.ui_controller.on_parameter_changed('color_style', text)
        )
        style_layout.addWidget(style_combo)
        
        color_layout.addLayout(style_layout)
        
        layout.addWidget(color_group)
        
        # 肤色控制组
        skin_group = QGroupBox("肤色")
        skin_layout = QVBoxLayout(skin_group)
        
        skin_checkbox = QCheckBox("启用肤色调整")
        skin_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('skin'))
        skin_layout.addWidget(skin_checkbox)
        
        skin_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('skin_strength', value)
        )
        skin_layout.addLayout(skin_slider)
        
        tone_slider = self._create_slider_control(
            "肤色", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('skin_tone', value)
        )
        skin_layout.addLayout(tone_slider)
        
        smoothness_slider = self._create_slider_control(
            "平滑度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('skin_smoothness', value)
        )
        skin_layout.addLayout(smoothness_slider)
        
        layout.addWidget(skin_group)
        
        # 添加弹性空间
        layout.addStretch(1)
        
        # 添加到标签页
        self.tab_widget.addTab(color_tab, "颜色调整")
    
    def _create_effects_tab(self) -> None:
        """创建特效标签页"""
        effects_tab = QWidget()
        layout = QVBoxLayout(effects_tab)
        layout.setSpacing(15)
        
        # 美颜控制组
        beauty_group = QGroupBox("美颜")
        beauty_layout = QVBoxLayout(beauty_group)
        
        beauty_checkbox = QCheckBox("启用美颜")
        beauty_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('beauty'))
        beauty_layout.addWidget(beauty_checkbox)
        
        beauty_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('beauty_strength', value)
        )
        beauty_layout.addLayout(beauty_slider)
        
        beauty_style_layout = QHBoxLayout()
        beauty_style_layout.addWidget(QLabel("美颜风格:"))
        
        beauty_style_combo = QComboBox()
        beauty_style_combo.addItems(["自然", "精致", "柔和", "清新"])
        beauty_style_combo.currentTextChanged.connect(
            lambda text: self.ui_controller.on_parameter_changed('beauty_style', text)
        )
        beauty_style_layout.addWidget(beauty_style_combo)
        
        beauty_layout.addLayout(beauty_style_layout)
        
        layout.addWidget(beauty_group)
        
        # 镜头效果控制组
        lens_group = QGroupBox("镜头效果")
        lens_layout = QVBoxLayout(lens_group)
        
        lens_checkbox = QCheckBox("启用镜头效果")
        lens_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('lens'))
        lens_layout.addWidget(lens_checkbox)
        
        lens_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('lens_strength', value)
        )
        lens_layout.addLayout(lens_slider)
        
        effect_layout = QHBoxLayout()
        effect_layout.addWidget(QLabel("效果:"))
        
        effect_combo = QComboBox()
        effect_combo.addItems(["景深", "柔焦", "星芒", "镜头光晕", "色差"])
        effect_combo.currentIndexChanged.connect(
            lambda index: self.ui_controller.on_parameter_changed('lens_effect', index)
        )
        effect_layout.addWidget(effect_combo)
        
        lens_layout.addLayout(effect_layout)
        
        aperture_slider = self._create_slider_control(
            "光圈", 10, 40, 20,
            lambda value: self.ui_controller.on_parameter_changed('lens_aperture', value / 10.0)
        )
        lens_layout.addLayout(aperture_slider)
        
        depth_slider = self._create_slider_control(
            "深度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('lens_depth', value / 100.0)
        )
        lens_layout.addLayout(depth_slider)
        
        layout.addWidget(lens_group)
        
        # 自动调整控制组
        auto_group = QGroupBox("自动调整")
        auto_layout = QVBoxLayout(auto_group)
        
        auto_adjust_checkbox = QCheckBox("启用自动调整")
        auto_adjust_checkbox.toggled.connect(lambda state: self.ui_controller.on_toggle_parameter('auto_adjust'))
        auto_layout.addWidget(auto_adjust_checkbox)
        
        auto_slider = self._create_slider_control(
            "强度", 0, 100, 50,
            lambda value: self.ui_controller.on_parameter_changed('auto_adjust_strength', value)
        )
        auto_layout.addLayout(auto_slider)
        
        layout.addWidget(auto_group)
        
        # 添加弹性空间
        layout.addStretch(1)
        
        # 添加到标签页
        self.tab_widget.addTab(effects_tab, "特效")

    def _create_vlm_analysis_tab(self) -> None:
        """创建VLM图像质量分析标签页"""
        vlm_tab = QWidget()
        layout = QVBoxLayout(vlm_tab)
        layout.setSpacing(15)

        # VLM分析控制组
        vlm_group = QGroupBox("图像质量分析 (VLM)")
        vlm_layout = QVBoxLayout(vlm_group)

        # 分析按钮
        analyze_button = QPushButton("分析图像质量")
        analyze_button.setMinimumHeight(40)
        analyze_button.clicked.connect(lambda: self.ui_controller.on_vlm_analysis_requested())
        vlm_layout.addWidget(analyze_button)

        # 质量评分显示区域
        scores_group = QGroupBox("质量评分")
        scores_layout = QVBoxLayout(scores_group)

        # 创建评分显示标签
        self.noise_score_label = QLabel("噪声控制: --")
        self.color_score_label = QLabel("颜色质量: --")
        self.sharpness_score_label = QLabel("锐度表现: --")
        self.contrast_score_label = QLabel("对比度: --")
        self.overall_score_label = QLabel("综合评分: --")

        # 设置字体
        score_font = QFont()
        score_font.setPointSize(10)
        for label in [self.noise_score_label, self.color_score_label,
                     self.sharpness_score_label, self.contrast_score_label]:
            label.setFont(score_font)
            scores_layout.addWidget(label)

        # 综合评分使用更大的字体
        overall_font = QFont()
        overall_font.setPointSize(12)
        overall_font.setBold(True)
        self.overall_score_label.setFont(overall_font)
        scores_layout.addWidget(self.overall_score_label)

        vlm_layout.addWidget(scores_group)

        # 详细分析结果显示区域
        analysis_group = QGroupBox("详细分析")
        analysis_layout = QVBoxLayout(analysis_group)

        # 创建文本显示区域
        from PyQt6.QtWidgets import QTextEdit
        self.analysis_text = QTextEdit()
        self.analysis_text.setReadOnly(True)
        self.analysis_text.setMaximumHeight(150)
        self.analysis_text.setPlainText("点击'分析图像质量'按钮开始分析...")
        analysis_layout.addWidget(self.analysis_text)

        vlm_layout.addWidget(analysis_group)

        layout.addWidget(vlm_group)

        # 添加弹性空间
        layout.addStretch(1)

        # 添加到标签页
        self.tab_widget.addTab(vlm_tab, "质量分析")

    def update_vlm_analysis_results(self, analysis_result: dict) -> None:
        """
        更新VLM分析结果显示

        Args:
            analysis_result: VLM分析结果字典
        """
        scores = analysis_result.get('scores', {})
        overall_score = analysis_result.get('overall_score', 0)
        analysis_text = analysis_result.get('analysis', '')

        # 更新评分显示
        self.noise_score_label.setText(f"噪声控制: {scores.get('noise', 0)}")
        self.color_score_label.setText(f"颜色质量: {scores.get('color', 0)}")
        self.sharpness_score_label.setText(f"锐度表现: {scores.get('sharpness', 0)}")
        self.contrast_score_label.setText(f"对比度: {scores.get('contrast', 0)}")
        self.overall_score_label.setText(f"综合评分: {overall_score:.1f}")

        # 更新详细分析文本
        if analysis_text:
            self.analysis_text.setPlainText(analysis_text)
        else:
            self.analysis_text.setPlainText("分析完成，但未获取到详细分析结果。")

    def _create_action_buttons(self) -> None:
        """创建操作按钮"""
        action_layout = QHBoxLayout()
        
        # 应用按钮
        apply_button = QPushButton("应用")
        apply_button.clicked.connect(self.ui_controller.on_apply)
        action_layout.addWidget(apply_button)
        
        # 重置按钮
        reset_button = QPushButton("重置")
        reset_button.clicked.connect(self.ui_controller.on_reset)
        action_layout.addWidget(reset_button)
        
        # 撤销按钮
        undo_button = QPushButton("撤销")
        undo_button.clicked.connect(self.ui_controller.on_undo)
        action_layout.addWidget(undo_button)
        
        self.main_layout.addLayout(action_layout)
    
    def _create_slider_control(self, label_text, min_value, max_value, default_value, value_changed_callback):
        """
        创建带标签的滑块控件
        
        Args:
            label_text: 标签文本
            min_value: 最小值
            max_value: 最大值
            default_value: 默认值
            value_changed_callback: 值变化回调函数
            
        Returns:
            包含滑块和标签的水平布局
        """
        layout = QHBoxLayout()
        
        # 标签
        label = QLabel(label_text)
        label.setMinimumWidth(60)
        layout.addWidget(label)
        
        # 滑块
        slider = QSlider(Qt.Orientation.Horizontal)
        slider.setMinimum(min_value)
        slider.setMaximum(max_value)
        slider.setValue(default_value)
        slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        slider.setTickInterval((max_value - min_value) // 5)
        slider.valueChanged.connect(value_changed_callback)
        layout.addWidget(slider)
        
        # 值标签
        value_label = QLabel(str(default_value))
        value_label.setMinimumWidth(30)
        slider.valueChanged.connect(lambda v: value_label.setText(str(v)))
        layout.addWidget(value_label)
        
        return layout 