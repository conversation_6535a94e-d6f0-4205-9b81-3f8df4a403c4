"""
推理引擎工厂模块

此模块提供了用于创建适合当前平台的推理引擎的工厂函数。
"""

import logging
from typing import Optional, Dict, Any

from .base_inference import ModelInferenceEngine
from .intel_inference import IntelInferenceEngine
from .apple_silicon_inference import AppleSiliconInferenceEngine
from .vlm_inference import VLMInferenceEngine
from ..platform_utils import is_apple_silicon, is_intel_mac

# 设置日志
logger = logging.getLogger(__name__)

# 缓存创建的推理引擎实例
_engine_instance = None

def create_inference_engine(force_platform: Optional[str] = None) -> ModelInferenceEngine:
    """
    创建适合当前平台的推理引擎
    
    工厂函数，根据当前系统平台自动选择合适的推理引擎实现。
    使用单例模式，避免重复创建。
    
    Args:
        force_platform: 可选，强制使用指定平台的推理引擎，可选值: 'apple', 'intel'
                       用于测试或特殊需求
    
    Returns:
        适合当前平台的推理引擎实例
    """
    global _engine_instance
    
    # 如果已经有实例且不需要强制指定平台，则返回现有实例
    if _engine_instance is not None and force_platform is None:
        return _engine_instance
    
    # 根据强制指定的平台或当前系统平台选择合适的推理引擎
    if force_platform == 'apple' or (force_platform is None and is_apple_silicon()):
        logger.info("创建Apple Silicon推理引擎")
        _engine_instance = AppleSiliconInferenceEngine()
    elif force_platform == 'intel' or (force_platform is None and is_intel_mac()):
        logger.info("创建Intel推理引擎")
        _engine_instance = IntelInferenceEngine()
    else:
        # 如果无法确定平台或不支持当前平台，默认使用Intel实现
        logger.warning(f"无法识别的平台或强制平台: {force_platform}，默认使用Intel推理引擎")
        _engine_instance = IntelInferenceEngine()
    
    return _engine_instance

def get_platform_capabilities() -> Dict[str, Any]:
    """
    获取当前平台支持的深度学习能力
    
    Returns:
        包含平台能力的字典，包括支持的模型类型、硬件加速等信息
    """
    engine = create_inference_engine()
    platform_name = engine.get_platform_name()
    platform_info = engine.get_platform_info()
    
    capabilities = {
        'platform': platform_name,
        'hardware_info': platform_info,
        'supports_neural_engine': is_apple_silicon(),
        'supports_openvino': is_intel_mac(),
    }
    
    return capabilities

def create_vlm_engine(model_path: Optional[str] = None) -> VLMInferenceEngine:
    """
    创建VLM推理引擎

    Args:
        model_path: VLM模型路径，如果为None则使用默认路径

    Returns:
        VLM推理引擎实例
    """
    logger.info("创建VLM推理引擎")
    return VLMInferenceEngine(model_path) if model_path else VLMInferenceEngine()