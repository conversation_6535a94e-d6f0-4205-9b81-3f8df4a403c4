"""
模型推理引擎基类

此模块定义了模型推理引擎的抽象基类，提供了通用接口。
"""

from abc import ABC, abstractmethod
from enum import Enum
import numpy as np
from typing import Dict, Any, Optional, Union, Tuple

class ModelType(Enum):
    """模型类型枚举"""
    SUPERRES = 'superres'
    DENOISE = 'denoise'
    BEAUTIFY = 'beautify'
    SEGMENT = 'segment'
    SHARPEN = 'sharpen'
    VLM = 'vlm'  # Vision-Language Model for image quality assessment

class ModelInferenceEngine(ABC):
    """
    模型推理引擎抽象基类
    
    所有特定平台的推理引擎实现都应该继承此类。
    """
    
    def __init__(self):
        """初始化推理引擎"""
        self._loaded_models = {}
        self._platform_info = None
        
    @abstractmethod
    def load_model(self, model_type: ModelType, model_path: Optional[str] = None) -> bool:
        """
        加载深度学习模型
        
        Args:
            model_type: 模型类型
            model_path: 模型文件路径，如果为None则使用默认路径
            
        Returns:
            加载是否成功
        """
        pass
    
    @abstractmethod
    def unload_model(self, model_type: ModelType) -> bool:
        """
        卸载模型
        
        Args:
            model_type: 要卸载的模型类型
            
        Returns:
            卸载是否成功
        """
        pass
    
    @abstractmethod
    def is_model_loaded(self, model_type: ModelType) -> bool:
        """
        检查模型是否已加载
        
        Args:
            model_type: 要检查的模型类型
            
        Returns:
            模型是否已加载
        """
        pass
        
    @abstractmethod
    def run_inference(self, model_type: ModelType, input_data: np.ndarray, 
                     params: Dict[str, Any] = None) -> np.ndarray:
        """
        运行模型推理
        
        Args:
            model_type: 使用的模型类型
            input_data: 输入数据，通常是图像数据
            params: 推理参数
            
        Returns:
            推理结果
        """
        pass
    
    @abstractmethod
    def get_platform_name(self) -> str:
        """
        获取平台名称
        
        Returns:
            平台名称标识符
        """
        pass
    
    @abstractmethod
    def get_platform_info(self) -> Dict[str, Any]:
        """
        获取平台详细信息
        
        Returns:
            包含平台详细信息的字典
        """
        pass
    
    @abstractmethod
    def supports_model(self, model_type: ModelType) -> bool:
        """
        检查平台是否支持指定的模型类型
        
        Args:
            model_type: 要检查的模型类型
            
        Returns:
            是否支持该模型
        """
        pass
    
    def run_superres(self, image: np.ndarray, scale: float = 2.0, 
                    quality: int = 2) -> np.ndarray:
        """
        运行超分辨率推理
        
        Args:
            image: 输入图像
            scale: 放大倍数
            quality: 质量参数(1-3)，越高质量越好但速度越慢
            
        Returns:
            超分辨率处理后的图像
        """
        params = {
            'scale': scale,
            'quality': quality
        }
        
        return self.run_inference(ModelType.SUPERRES, image, params)

    def run_vlm_analysis(self, image: np.ndarray, prompt: str = None,
                        temperature: float = 0.0, max_tokens: int = 10240) -> str:
        """
        运行VLM图像质量分析

        Args:
            image: 输入图像
            prompt: 分析提示词，如果为None则使用默认质量评估提示
            temperature: 生成温度参数
            max_tokens: 最大生成token数

        Returns:
            VLM分析结果文本
        """
        params = {
            'prompt': prompt,
            'temperature': temperature,
            'max_tokens': max_tokens
        }

        result = self.run_inference(ModelType.VLM, image, params)
        # VLM返回的是文本结果，不是图像数组
        return result if isinstance(result, str) else str(result)