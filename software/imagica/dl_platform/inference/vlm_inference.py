"""
VLM (Vision-Language Model) 推理引擎

此模块提供基于mlx-vlm的图像质量分析功能，集成到现有的推理引擎架构中。
"""

import os
import numpy as np
from typing import Dict, Any, Optional, List
from PIL import Image
import tempfile

from .base_inference import ModelInferenceEngine, ModelType

try:
    import mlx.core as mx
    from mlx_vlm import load, generate
    from mlx_vlm.prompt_utils import apply_chat_template
    from mlx_vlm.utils import load_config
    MLX_VLM_AVAILABLE = True
except ImportError:
    MLX_VLM_AVAILABLE = False
    print("Warning: mlx-vlm not available. VLM functionality will be disabled.")


class VLMInferenceEngine(ModelInferenceEngine):
    """
    VLM推理引擎实现
    
    基于mlx-vlm提供图像质量分析功能
    """
    
    def __init__(self, model_path: str = "llava-fastvithd_1.5b_stage3_llm.int8"):
        """
        初始化VLM推理引擎
        
        Args:
            model_path: VLM模型路径
        """
        super().__init__()
        self.model_path = model_path
        self.model = None
        self.processor = None
        self.config = None
        self._platform_info = {
            'name': 'VLM',
            'backend': 'mlx-vlm',
            'available': MLX_VLM_AVAILABLE
        }
        
        # 默认的图像质量评估提示词
        self.default_quality_prompt = """
You are a Vision‑Language Model tasked with evaluating the technical quality of an input image. Follow these steps EXACTLY:

1. **Noise Analysis**  
   - Examine the image for grain, speckles, or chroma noise artifacts.  
   - Explain in 2–3 sentences: e.g. "I see moderate luminance noise in flat areas, especially in shadows. The grain is uniform and slightly colored."  
   - Based on your explanation, assign a noise score from 0 (extremely noisy) to 100 (virtually noise‑free).

2. **Color Analysis**  
   - Assess color accuracy, saturation, and balance. Look for color casts, oversaturation, or desaturation.  
   - Explain in 2–3 sentences: e.g. "Colors appear slightly warm, with skin tones leaning orange; saturation is strong but not clipped."  
   - Assign a color score from 0 (poor color) to 100 (perfectly balanced, accurate color).

3. **Sharpness Analysis**  
   - Judge edge definition, detail clarity, and presence of blur (motion or focus).  
   - Explain in 2–3 sentences: e.g. "Fine details are clear, edges are crisp; minor softness around the background indicates slight defocus."  
   - Assign a sharpness score from 0 (entirely blurry) to 100 (razor‑sharp everywhere).

4. **Contrast Analysis**  
   - Evaluate the tonal range and separation between highlights and shadows.  
   - Explain in 2–3 sentences: e.g. "The image has strong midtone contrast but slightly clipped highlights in bright areas."  
   - Assign a contrast score from 0 (flat, no contrast) to 100 (excellent tonal separation).

5. **JSON Summary**  
   - After you've given explanations and scores for each aspect, output ONLY a JSON object summarizing the scores, in this exact format:
   ```json
   {
     "noise": <noise_score>,
     "color": <color_score>,
     "sharpness": <sharpness_score>,
     "contrast": <contrast_score>
   }
   ```
"""
    
    def load_model(self, model_type: ModelType, model_path: Optional[str] = None) -> bool:
        """
        加载VLM模型
        
        Args:
            model_type: 模型类型，应该是ModelType.VLM
            model_path: 模型路径，如果为None则使用默认路径
            
        Returns:
            加载是否成功
        """
        if not MLX_VLM_AVAILABLE:
            print("Error: mlx-vlm is not available. Cannot load VLM model.")
            return False
            
        if model_type != ModelType.VLM:
            return False
            
        try:
            # 使用提供的路径或默认路径
            path = model_path or self.model_path
            
            # 加载模型
            self.model, self.processor = load(path)
            self.config = load_config(path)
            
            # 标记模型已加载
            self._loaded_models[ModelType.VLM] = True
            
            print(f"VLM model loaded successfully from: {path}")
            return True
            
        except Exception as e:
            print(f"Error loading VLM model: {e}")
            return False
    
    def unload_model(self, model_type: ModelType) -> bool:
        """
        卸载VLM模型
        
        Args:
            model_type: 要卸载的模型类型
            
        Returns:
            卸载是否成功
        """
        if model_type == ModelType.VLM and model_type in self._loaded_models:
            self.model = None
            self.processor = None
            self.config = None
            del self._loaded_models[model_type]
            return True
        return False
    
    def is_model_loaded(self, model_type: ModelType) -> bool:
        """
        检查VLM模型是否已加载
        
        Args:
            model_type: 要检查的模型类型
            
        Returns:
            模型是否已加载
        """
        return model_type == ModelType.VLM and model_type in self._loaded_models
    
    def run_inference(self, model_type: ModelType, input_data: np.ndarray, 
                     params: Dict[str, Any] = None) -> str:
        """
        运行VLM推理
        
        Args:
            model_type: 使用的模型类型
            input_data: 输入图像数据
            params: 推理参数，包含prompt, temperature, max_tokens等
            
        Returns:
            VLM分析结果文本
        """
        if not self.is_model_loaded(ModelType.VLM):
            raise RuntimeError("VLM model is not loaded")
            
        if model_type != ModelType.VLM:
            raise ValueError(f"Unsupported model type: {model_type}")
        
        # 解析参数
        params = params or {}
        prompt = params.get('prompt', self.default_quality_prompt)
        temperature = params.get('temperature', 0.0)
        max_tokens = params.get('max_tokens', 10240)
        
        try:
            # 将numpy数组转换为PIL图像并保存为临时文件
            if isinstance(input_data, np.ndarray):
                # 确保数据类型正确
                if input_data.dtype != np.uint8:
                    input_data = (input_data * 255).astype(np.uint8)
                
                # 转换为PIL图像
                if len(input_data.shape) == 3:
                    if input_data.shape[2] == 3:  # RGB
                        pil_image = Image.fromarray(input_data, 'RGB')
                    elif input_data.shape[2] == 4:  # RGBA
                        pil_image = Image.fromarray(input_data, 'RGBA')
                    else:
                        raise ValueError(f"Unsupported number of channels: {input_data.shape[2]}")
                else:
                    raise ValueError(f"Unsupported image shape: {input_data.shape}")
                
                # 保存为临时文件
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                    pil_image.save(tmp_file.name, 'JPEG')
                    image_path = tmp_file.name
            else:
                raise ValueError("Input data must be a numpy array")
            
            # 准备图像列表
            image = [image_path]
            
            # 应用聊天模板
            formatted_prompt = apply_chat_template(
                self.processor, self.config, prompt, num_images=len(image)
            )
            
            # 生成输出
            output = generate(
                self.model, 
                self.processor, 
                formatted_prompt, 
                image, 
                temperature=temperature, 
                max_tokens=max_tokens, 
                verbose=False
            )
            
            # 清理临时文件
            try:
                os.unlink(image_path)
            except:
                pass
            
            return output
            
        except Exception as e:
            print(f"Error during VLM inference: {e}")
            raise
    
    def get_platform_name(self) -> str:
        """获取平台名称"""
        return "VLM"
    
    def get_platform_info(self) -> Dict[str, Any]:
        """获取平台详细信息"""
        return self._platform_info.copy()
    
    def supports_model(self, model_type: ModelType) -> bool:
        """检查是否支持指定的模型类型"""
        return model_type == ModelType.VLM and MLX_VLM_AVAILABLE
