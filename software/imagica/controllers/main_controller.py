"""
主控制器模块

此模块负责协调所有控制器和模型，包括：
1. 初始化应用程序
2. 协调不同控制器之间的交互
3. 管理应用程序状态
"""

import os
import numpy as np
from typing import Optional, Dict, Any

from models.image_model import ImageModel
from models.processing_model import ProcessingModel
from models.settings_model import SettingsModel
from services.file_service import FileService
from services.image_processor_service import ImageProcessorService
from controllers.file_controller import FileController
from controllers.image_processing_controller import ImageProcessingController

class MainController:
    """主控制器类，负责协调所有控制器和模型"""
    
    def __init__(self, settings_file: str = None):
        """
        初始化主控制器
        
        Args:
            settings_file: 设置文件路径
        """
        # 初始化模型
        self.image_model = ImageModel()
        self.processing_model = ProcessingModel()
        self.settings_model = SettingsModel(settings_file)
        
        # 初始化服务
        self.file_service = FileService()
        self.processor_service = ImageProcessorService()
        
        # 初始化子控制器
        self.file_controller = FileController(
            self.image_model,
            self.settings_model,
            self.file_service
        )
        
        self.processing_controller = ImageProcessingController(
            self.image_model,
            self.processing_model,
            self.processor_service
        )
        
        # 设置初始应用状态
        self._initialize_app_state()
        
        # 注册模型观察者
        self.image_model.register_observer(self)
        self.settings_model.register_observer(self)
    
    def _initialize_app_state(self) -> None:
        """初始化应用程序状态"""
        # 从设置中加载自动应用参数
        auto_apply = self.settings_model.get_setting('auto_apply', True)
        self.processing_controller.set_auto_apply(auto_apply)
    
    def update(self, subject, event_type: str, **kwargs) -> None:
        """
        观察者模式更新方法，响应模型变化
        
        Args:
            subject: 被观察的对象
            event_type: 事件类型
            **kwargs: 额外参数
        """
        # 处理设置变化事件
        if subject is self.settings_model and event_type == 'setting_changed':
            key = kwargs.get('key', '')
            value = kwargs.get('value')
            
            # 处理自动应用设置变化
            if key == 'auto_apply':
                self.processing_controller.set_auto_apply(value)
    
    def open_image(self, file_path: str) -> bool:
        """
        打开图像文件
        
        Args:
            file_path: 图像文件路径
            
        Returns:
            是否成功打开
        """
        return self.file_controller.open_image(file_path)
    
    def save_image(self, file_path: str = None, save_params: Dict = None) -> bool:
        """
        保存图像到文件
        
        Args:
            file_path: 保存路径，如果为None则使用当前文件路径
            save_params: 保存参数
            
        Returns:
            是否成功保存
        """
        # 如果设置了自动备份原始图像
        if self.settings_model.get_setting('backup_original', True):
            self.file_controller.backup_original()
            
        return self.file_controller.save_image(file_path, save_params)
    
    def reset_all(self) -> None:
        """重置所有处理和图像"""
        self.processing_controller.reset_processing()
    
    def undo(self) -> bool:
        """
        撤销上一次处理
        
        Returns:
            是否成功撤销
        """
        return self.processing_controller.undo_processing()
    
    def apply_processing(self) -> bool:
        """
        应用当前处理参数
        
        Returns:
            是否成功应用
        """
        return self.processing_controller.apply_processing()
    
    def auto_enhance(self) -> bool:
        """
        自动增强图像
        
        Returns:
            是否成功增强
        """
        return self.processing_controller.auto_enhance()
    
    def auto_beauty(self) -> bool:
        """
        自动美颜处理

        Returns:
            是否成功处理
        """
        return self.processing_controller.auto_beauty()

    def analyze_image_quality(self, image: np.ndarray = None) -> Optional[Dict[str, Any]]:
        """
        分析图像质量

        Args:
            image: 要分析的图像，如果为None则使用当前图像

        Returns:
            包含质量分析结果的字典，如果失败则返回None
        """
        try:
            # 如果没有提供图像，使用当前图像
            if image is None:
                image = self.get_current_image()

            if image is None:
                return None

            # 调用图像处理服务进行VLM分析
            analysis_result = self.image_processor_service.analyze_image_quality(image)
            return analysis_result

        except Exception as e:
            print(f"Error in image quality analysis: {e}")
            return None
    
    def set_parameter(self, param_name: str, value: Any) -> None:
        """
        设置处理参数值
        
        Args:
            param_name: 参数名
            value: 参数值
        """
        self.processing_controller.set_parameter(param_name, value)
    
    def get_parameter(self, param_name: str, default: Any = None) -> Any:
        """
        获取处理参数值
        
        Args:
            param_name: 参数名
            default: 默认值
            
        Returns:
            参数值
        """
        return self.processing_controller.get_parameter(param_name, default)
    
    def toggle_parameter(self, param_name: str) -> bool:
        """
        切换处理参数的开关状态
        
        Args:
            param_name: 参数名
            
        Returns:
            切换后的状态
        """
        return self.processing_controller.toggle_parameter(param_name)
    
    def set_setting(self, setting_name: str, value: Any) -> None:
        """
        设置应用设置值
        
        Args:
            setting_name: 设置名
            value: 设置值
        """
        self.settings_model.set_setting(setting_name, value)
    
    def get_setting(self, setting_name: str, default: Any = None) -> Any:
        """
        获取应用设置值
        
        Args:
            setting_name: 设置名
            default: 默认值
            
        Returns:
            设置值
        """
        return self.settings_model.get_setting(setting_name, default)
    
    def save_settings(self) -> bool:
        """
        保存应用设置
        
        Returns:
            是否成功保存
        """
        return self.settings_model.save_settings()
    
    def get_image(self) -> Any:
        """
        获取当前图像
        
        Returns:
            当前图像
        """
        return self.image_model.get_current_image()
    
    def get_original_image(self) -> Any:
        """
        获取原始图像
        
        Returns:
            原始图像
        """
        return self.image_model.get_original_image()
    
    def has_image(self) -> bool:
        """
        检查是否有图像
        
        Returns:
            是否有图像
        """
        return self.image_model.has_image()
    
    def shutdown(self) -> None:
        """应用程序关闭前的清理工作"""
        # 保存设置
        self.save_settings()
        
        # 解注册观察者
        self.image_model.remove_observer(self)
        self.settings_model.remove_observer(self)