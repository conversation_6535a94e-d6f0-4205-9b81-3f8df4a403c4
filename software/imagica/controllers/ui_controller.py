"""
UI控制器模块

此模块负责处理用户界面交互，包括：
1. 响应用户界面事件
2. 更新用户界面显示
3. 协调用户界面和业务逻辑
"""

from typing import Optional, Dict, Any, Callable
import numpy as np
from PyQt6.QtCore import Qt, QPoint
from PyQt6.QtGui import QPixmap, QImage

from controllers.main_controller import MainController

class UIController:
    """UI控制器类，负责处理用户界面交互"""
    
    def __init__(self, main_controller: MainController):
        """
        初始化UI控制器
        
        Args:
            main_controller: 主控制器
        """
        self.main_controller = main_controller
        
        # 缓存一些UI相关的状态
        self.zoom_factor = 1.0  # 缩放因子
        self.image_offset = QPoint(0, 0)  # 图像偏移
        self.is_panning = False  # 是否正在平移
        self.pan_start_pos = None  # 平移起始位置
        self.showing_original = False  # 是否正在显示原始图像
        self.comparing_mode = False  # 是否处于对比模式
        
        # 注册模型观察者
        self.main_controller.image_model.register_observer(self)
        
        # UI回调函数
        self._image_display_callbacks = []  # 图像显示回调函数列表
        self._status_update_callbacks = []  # 状态更新回调函数列表
        self._ui_state_callbacks = []  # UI状态回调函数列表
        self._vlm_analysis_callbacks = []  # VLM分析结果回调函数列表
    
    def update(self, subject, event_type: str, **kwargs) -> None:
        """
        观察者模式更新方法，响应模型变化
        
        Args:
            subject: 被观察的对象
            event_type: 事件类型
            **kwargs: 额外参数
        """
        # 处理图像模型变化事件
        if subject is self.main_controller.image_model:
            if event_type in ['image_changed', 'image_updated', 'image_reset']:
                self._notify_image_display()
    
    def _notify_image_display(self) -> None:
        """通知图像显示需要更新"""
        image = self.main_controller.get_image()
        if image is not None:
            for callback in self._image_display_callbacks:
                callback(image)
    
    def _notify_status_update(self, status_text: str) -> None:
        """通知状态栏需要更新"""
        for callback in self._status_update_callbacks:
            callback(status_text)
    
    def _notify_ui_state(self, state_type: str, state_value: Any) -> None:
        """通知UI状态需要更新"""
        for callback in self._ui_state_callbacks:
            callback(state_type, state_value)

    def _notify_vlm_analysis_result(self, analysis_result: Dict[str, Any]) -> None:
        """通知VLM分析结果需要更新"""
        for callback in self._vlm_analysis_callbacks:
            callback(analysis_result)
    
    def register_image_display_callback(self, callback: Callable[[np.ndarray], None]) -> None:
        """
        注册图像显示回调函数
        
        Args:
            callback: 图像显示回调函数，接收图像数据作为参数
        """
        if callback not in self._image_display_callbacks:
            self._image_display_callbacks.append(callback)
    
    def register_status_update_callback(self, callback: Callable[[str], None]) -> None:
        """
        注册状态更新回调函数
        
        Args:
            callback: 状态更新回调函数，接收状态文本作为参数
        """
        if callback not in self._status_update_callbacks:
            self._status_update_callbacks.append(callback)
    
    def register_ui_state_callback(self, callback: Callable[[str, Any], None]) -> None:
        """
        注册UI状态回调函数

        Args:
            callback: UI状态回调函数，接收状态类型和值作为参数
        """
        if callback not in self._ui_state_callbacks:
            self._ui_state_callbacks.append(callback)

    def register_vlm_analysis_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        注册VLM分析结果回调函数

        Args:
            callback: VLM分析结果回调函数，接收分析结果字典作为参数
        """
        if callback not in self._vlm_analysis_callbacks:
            self._vlm_analysis_callbacks.append(callback)
    
    def on_open_image(self) -> None:
        """处理打开图像事件"""
        # 由视图层处理文件选择对话框
        pass
    
    def on_save_image(self) -> None:
        """处理保存图像事件"""
        # 由视图层处理文件选择对话框
        pass
    
    def on_parameter_changed(self, param_name: str, value: Any) -> None:
        """
        处理参数变化事件
        
        Args:
            param_name: 参数名
            value: 参数值
        """
        self.main_controller.set_parameter(param_name, value)
        self._notify_status_update(f"参数 {param_name} 已设置为 {value}")
    
    def on_toggle_parameter(self, param_name: str) -> None:
        """
        处理参数切换事件
        
        Args:
            param_name: 参数名
        """
        state = self.main_controller.toggle_parameter(param_name)
        status = "启用" if state else "禁用"
        self._notify_status_update(f"{param_name} 已{status}")
    
    def on_zoom_in(self) -> None:
        """处理放大事件"""
        max_zoom = 5.0
        zoom_step = 0.2
        self.zoom_factor = min(self.zoom_factor + zoom_step, max_zoom)
        self._notify_ui_state('zoom_changed', self.zoom_factor)
        self._notify_image_display()
    
    def on_zoom_out(self) -> None:
        """处理缩小事件"""
        min_zoom = 0.1
        zoom_step = 0.2
        self.zoom_factor = max(self.zoom_factor - zoom_step, min_zoom)
        self._notify_ui_state('zoom_changed', self.zoom_factor)
        self._notify_image_display()
    
    def on_fit_to_window(self) -> None:
        """处理适应窗口事件"""
        self.zoom_factor = -1.0  # 特殊值表示适应窗口
        self._notify_ui_state('zoom_changed', self.zoom_factor)
        self._notify_image_display()
    
    def on_actual_size(self) -> None:
        """处理实际大小事件"""
        self.zoom_factor = 1.0
        self._notify_ui_state('zoom_changed', self.zoom_factor)
        self._notify_image_display()
    
    def on_pan_start(self, pos: QPoint) -> None:
        """
        处理平移开始事件
        
        Args:
            pos: 起始位置
        """
        self.is_panning = True
        self.pan_start_pos = pos
    
    def on_pan_move(self, pos: QPoint) -> None:
        """
        处理平移移动事件
        
        Args:
            pos: 当前位置
        """
        if self.is_panning and self.pan_start_pos is not None:
            # 计算偏移增量
            delta = pos - self.pan_start_pos
            self.image_offset += delta
            self.pan_start_pos = pos
            
            # 通知UI更新
            self._notify_ui_state('offset_changed', self.image_offset)
            self._notify_image_display()
    
    def on_pan_end(self) -> None:
        """处理平移结束事件"""
        self.is_panning = False
        self.pan_start_pos = None
    
    def on_reset(self) -> None:
        """处理重置事件"""
        self.main_controller.reset_all()
        self._notify_status_update("所有处理已重置")
    
    def on_undo(self) -> None:
        """处理撤销事件"""
        if self.main_controller.undo():
            self._notify_status_update("已撤销上一步操作")
        else:
            self._notify_status_update("无法撤销")
    
    def on_apply(self) -> None:
        """处理应用事件"""
        if self.main_controller.apply_processing():
            self._notify_status_update("已应用处理")
        else:
            self._notify_status_update("应用处理失败")
    
    def on_auto_enhance(self) -> None:
        """处理自动增强事件"""
        if self.main_controller.auto_enhance():
            self._notify_status_update("已应用自动增强")
        else:
            self._notify_status_update("自动增强失败")
    
    def on_auto_beauty(self) -> None:
        """处理自动美颜事件"""
        if self.main_controller.auto_beauty():
            self._notify_status_update("已应用自动美颜")
        else:
            self._notify_status_update("自动美颜失败")

    def on_vlm_analysis_requested(self) -> None:
        """处理VLM图像质量分析请求事件"""
        try:
            self._notify_status_update("正在分析图像质量...")

            # 获取当前图像
            current_image = self.main_controller.get_current_image()
            if current_image is None:
                self._notify_status_update("没有可分析的图像")
                return

            # 执行VLM分析
            analysis_result = self.main_controller.analyze_image_quality(current_image)

            if analysis_result:
                # 通知UI更新分析结果
                self._notify_vlm_analysis_result(analysis_result)

                overall_score = analysis_result.get('overall_score', 0)
                self._notify_status_update(f"图像质量分析完成，综合评分: {overall_score:.1f}")
            else:
                self._notify_status_update("图像质量分析失败")

        except Exception as e:
            self._notify_status_update(f"分析过程中出现错误: {str(e)}")

    def on_toggle_original(self, show_original: bool) -> None:
        """
        处理显示原始图像事件
        
        Args:
            show_original: 是否显示原始图像
        """
        self.showing_original = show_original
        
        if show_original:
            # 显示原始图像
            image = self.main_controller.get_original_image()
            if image is not None:
                for callback in self._image_display_callbacks:
                    callback(image)
            self._notify_status_update("显示原始图像")
        else:
            # 显示处理后的图像
            image = self.main_controller.get_image()
            if image is not None:
                for callback in self._image_display_callbacks:
                    callback(image)
            self._notify_status_update("显示处理后的图像")
    
    def on_toggle_compare(self, enable_compare: bool) -> None:
        """
        处理对比模式事件
        
        Args:
            enable_compare: 是否启用对比模式
        """
        self.comparing_mode = enable_compare
        self._notify_ui_state('compare_mode', enable_compare)
        
        if enable_compare:
            self._notify_status_update("对比模式已启用")
        else:
            self._notify_status_update("对比模式已禁用")
            
        # 重新显示图像
        self._notify_image_display()
    
    def on_setting_changed(self, setting_name: str, value: Any) -> None:
        """
        处理设置变化事件
        
        Args:
            setting_name: 设置名
            value: 设置值
        """
        self.main_controller.set_setting(setting_name, value)
        
        # 特殊处理某些设置
        if setting_name == 'control_panel_position':
            self._notify_ui_state('panel_position', value)
        elif setting_name == 'theme':
            self._notify_ui_state('theme', value)
        
        self._notify_status_update(f"设置 {setting_name} 已更新")
    
    def get_image_for_display(self) -> Optional[np.ndarray]:
        """
        获取用于显示的图像
        
        Returns:
            用于显示的图像
        """
        if self.showing_original:
            return self.main_controller.get_original_image()
        else:
            return self.main_controller.get_image()
    
    def convert_to_pixmap(self, image: np.ndarray) -> Optional[QPixmap]:
        """
        将图像转换为QPixmap用于显示
        
        Args:
            image: RGB格式的图像
            
        Returns:
            QPixmap对象
        """
        if image is None:
            return None
            
        height, width, channels = image.shape
        bytes_per_line = channels * width
        q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
        return QPixmap.fromImage(q_image)
    
    def get_zoom_factor(self) -> float:
        """
        获取缩放因子
        
        Returns:
            缩放因子
        """
        return self.zoom_factor
    
    def get_image_offset(self) -> QPoint:
        """
        获取图像偏移
        
        Returns:
            图像偏移
        """
        return self.image_offset
    
    def get_comparing_mode(self) -> bool:
        """
        获取是否处于对比模式
        
        Returns:
            是否处于对比模式
        """
        return self.comparing_mode
    
    def get_showing_original(self) -> bool:
        """
        获取是否正在显示原始图像
        
        Returns:
            是否正在显示原始图像
        """
        return self.showing_original 