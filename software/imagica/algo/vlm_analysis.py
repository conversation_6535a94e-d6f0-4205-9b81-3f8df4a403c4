"""
VLM图像质量分析算法模块

此模块提供基于Vision-Language Model的图像质量分析功能，
包括噪声、颜色、锐度、对比度等多维度评估。
"""

import cv2
import numpy as np
import json
import re
from typing import Dict, Any, Optional, Tuple

try:
    from dl_platform.inference.inference_factory import create_vlm_engine
    from dl_platform.inference.base_inference import ModelType
    VLM_AVAILABLE = True
except ImportError:
    VLM_AVAILABLE = False
    print("Warning: VLM inference not available")


def apply_vlm_analysis(image: np.ndarray, 
                      prompt: Optional[str] = None,
                      return_detailed: bool = True) -> Dict[str, Any]:
    """
    使用VLM进行图像质量分析
    
    Args:
        image: 输入图像 (numpy数组，BGR格式)
        prompt: 自定义分析提示词，如果为None则使用默认提示
        return_detailed: 是否返回详细分析结果
        
    Returns:
        包含质量分析结果的字典，格式如下:
        {
            'scores': {
                'noise': int,      # 噪声评分 (0-100)
                'color': int,      # 颜色评分 (0-100)
                'sharpness': int,  # 锐度评分 (0-100)
                'contrast': int    # 对比度评分 (0-100)
            },
            'analysis': str,       # 详细分析文本 (如果return_detailed=True)
            'overall_score': float # 综合评分 (0-100)
        }
    """
    if not VLM_AVAILABLE:
        # 如果VLM不可用，返回基于传统算法的简单评估
        return _fallback_analysis(image)
    
    try:
        # 创建VLM推理引擎
        vlm_engine = create_vlm_engine()
        
        # 加载VLM模型
        if not vlm_engine.is_model_loaded(ModelType.VLM):
            success = vlm_engine.load_model(ModelType.VLM)
            if not success:
                print("Failed to load VLM model, falling back to traditional analysis")
                return _fallback_analysis(image)
        
        # 将BGR转换为RGB (VLM期望RGB格式)
        if len(image.shape) == 3 and image.shape[2] == 3:
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            rgb_image = image
        
        # 运行VLM分析
        analysis_result = vlm_engine.run_vlm_analysis(
            rgb_image, 
            prompt=prompt,
            temperature=0.0,
            max_tokens=10240
        )
        
        # 解析VLM结果
        scores, detailed_analysis = _parse_vlm_result(analysis_result)
        
        # 计算综合评分
        overall_score = _calculate_overall_score(scores)
        
        result = {
            'scores': scores,
            'overall_score': overall_score
        }
        
        if return_detailed:
            result['analysis'] = detailed_analysis
            
        return result
        
    except Exception as e:
        print(f"Error in VLM analysis: {e}")
        return _fallback_analysis(image)


def _parse_vlm_result(vlm_output: str) -> Tuple[Dict[str, int], str]:
    """
    解析VLM输出结果，提取评分和详细分析
    
    Args:
        vlm_output: VLM的原始输出文本
        
    Returns:
        (scores_dict, detailed_analysis)
    """
    scores = {'noise': 50, 'color': 50, 'sharpness': 50, 'contrast': 50}
    detailed_analysis = vlm_output
    
    try:
        # 尝试从输出中提取JSON格式的评分
        json_pattern = r'```json\s*(\{[^}]*\})\s*```'
        json_match = re.search(json_pattern, vlm_output, re.DOTALL)
        
        if json_match:
            json_str = json_match.group(1)
            parsed_scores = json.loads(json_str)
            
            # 更新评分，确保在0-100范围内
            for key in ['noise', 'color', 'sharpness', 'contrast']:
                if key in parsed_scores:
                    score = parsed_scores[key]
                    if isinstance(score, (int, float)):
                        scores[key] = max(0, min(100, int(score)))
        else:
            # 如果没有找到JSON，尝试从文本中提取数字评分
            for key in ['noise', 'color', 'sharpness', 'contrast']:
                pattern = rf'{key}[:\s]*(\d+)'
                match = re.search(pattern, vlm_output, re.IGNORECASE)
                if match:
                    score = int(match.group(1))
                    scores[key] = max(0, min(100, score))
                    
    except (json.JSONDecodeError, ValueError) as e:
        print(f"Error parsing VLM result: {e}")
        # 保持默认评分
    
    return scores, detailed_analysis


def _calculate_overall_score(scores: Dict[str, int]) -> float:
    """
    计算综合质量评分
    
    Args:
        scores: 各维度评分字典
        
    Returns:
        综合评分 (0-100)
    """
    # 可以根据需要调整各维度的权重
    weights = {
        'noise': 0.25,      # 噪声权重
        'color': 0.25,      # 颜色权重
        'sharpness': 0.25,  # 锐度权重
        'contrast': 0.25    # 对比度权重
    }
    
    weighted_sum = sum(scores[key] * weights[key] for key in scores if key in weights)
    return round(weighted_sum, 2)


def _fallback_analysis(image: np.ndarray) -> Dict[str, Any]:
    """
    当VLM不可用时的备用分析方法，使用传统图像处理算法
    
    Args:
        image: 输入图像
        
    Returns:
        简化的质量分析结果
    """
    try:
        # 转换为灰度图像进行分析
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 简单的质量评估
        # 噪声评估 (基于拉普拉斯方差)
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        noise_score = max(0, min(100, int(100 - laplacian_var / 10)))
        
        # 锐度评估 (基于梯度幅值)
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(sobelx**2 + sobely**2)
        sharpness_score = max(0, min(100, int(np.mean(gradient_magnitude) * 2)))
        
        # 对比度评估 (基于标准差)
        contrast_score = max(0, min(100, int(np.std(gray) * 2)))
        
        # 颜色评估 (简化版本)
        if len(image.shape) == 3:
            color_std = np.mean([np.std(image[:,:,i]) for i in range(3)])
            color_score = max(0, min(100, int(color_std * 1.5)))
        else:
            color_score = 50  # 灰度图像默认评分
        
        scores = {
            'noise': noise_score,
            'color': color_score,
            'sharpness': sharpness_score,
            'contrast': contrast_score
        }
        
        overall_score = _calculate_overall_score(scores)
        
        return {
            'scores': scores,
            'overall_score': overall_score,
            'analysis': 'Traditional algorithm analysis (VLM not available)'
        }
        
    except Exception as e:
        print(f"Error in fallback analysis: {e}")
        # 返回默认评分
        default_scores = {'noise': 50, 'color': 50, 'sharpness': 50, 'contrast': 50}
        return {
            'scores': default_scores,
            'overall_score': 50.0,
            'analysis': 'Analysis failed, using default scores'
        }


def get_quality_summary(analysis_result: Dict[str, Any]) -> str:
    """
    生成质量分析摘要文本
    
    Args:
        analysis_result: apply_vlm_analysis的返回结果
        
    Returns:
        质量摘要文本
    """
    scores = analysis_result.get('scores', {})
    overall = analysis_result.get('overall_score', 0)
    
    # 生成评级
    def get_grade(score):
        if score >= 90: return "优秀"
        elif score >= 80: return "良好"
        elif score >= 70: return "中等"
        elif score >= 60: return "一般"
        else: return "较差"
    
    summary_parts = [
        f"综合评分: {overall:.1f} ({get_grade(overall)})",
        f"噪声控制: {scores.get('noise', 0)} ({get_grade(scores.get('noise', 0))})",
        f"颜色质量: {scores.get('color', 0)} ({get_grade(scores.get('color', 0))})",
        f"锐度表现: {scores.get('sharpness', 0)} ({get_grade(scores.get('sharpness', 0))})",
        f"对比度: {scores.get('contrast', 0)} ({get_grade(scores.get('contrast', 0))})"
    ]
    
    return "\n".join(summary_parts)
