name: Test PRs

on:
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: macos-14

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Install MLX
        run: |
          pip install mlx>=0.15

      - name: Install pre-commit
        run: |
          python -m pip install pre-commit
          pre-commit run --all
          if ! git diff --quiet; then
            echo 'Style checks failed, please install pre-commit and run pre-commit run --all and push the change'
            exit 1
          fi

      - name: Install package and dependencies
        run: |
          python -m pip install pytest
          python -m pip install -e .

      - name: Run Python tests
        run: |
          cd mlx_vlm/
          pytest -s ./tests --ignore=tests/test_smoke.py
