{"architectures": ["LlavaQwen2ForCausalLM"], "attention_dropout": 0.0, "bos_token_id": 151643, "eos_token_id": 151645, "freeze_mm_mlp_adapter": false, "hidden_act": "silu", "hidden_size": 1536, "image_aspect_ratio": "pad", "image_grid_pinpoints": null, "image_token_index": 151646, "initializer_range": 0.02, "intermediate_size": 8960, "max_position_embeddings": 32768, "max_window_layers": 28, "mm_hidden_size": 3072, "mm_patch_merge_type": "flat", "mm_projector_lr": null, "mm_projector_type": "mlp2x_gelu", "mm_use_im_patch_token": false, "mm_use_im_start_end": false, "mm_vision_select_feature": "patch", "mm_vision_select_layer": -2, "mm_vision_tower": "mobileclip_l_1024", "model_type": "llava_qwen2", "num_attention_heads": 12, "num_hidden_layers": 28, "num_key_value_heads": 2, "quantization": {"group_size": 64, "bits": 8}, "rms_norm_eps": 1e-06, "rope_theta": 1000000.0, "sliding_window": 32768, "tie_word_embeddings": false, "tokenizer_model_max_length": 8192, "tokenizer_padding_side": "right", "torch_dtype": "bfloat16", "transformers_version": "4.39.3", "tune_mm_mlp_adapter": false, "unfreeze_mm_vision_tower": true, "use_cache": true, "use_mm_proj": true, "use_sliding_window": false, "vision_config": {}, "vocab_size": 151936}