import mlx.core as mx
from mlx_vlm import load, generate
from mlx_vlm.prompt_utils import apply_chat_template
from mlx_vlm.utils import load_config

# Load the model
model_path = "llava-0.5b-exported"
model, processor = load(model_path)
config = load_config(model_path)

# Prepare input
image = ["images/3.jpg"]
# image = [Image.open("...")] can also be used with PIL.Image.Image objects
prompt = """You are a visual-quality-assessment model. Given the input image, evaluate its quality along these five dimensions:
  • “noise”: degree of visible grain or sensor noise (score 0–100, where 0 = extremely noisy, 100 = noise-free).
  • “sharpness”: clarity of edges and fine details (score 0–100, where 0 = completely blurry, 100 = razor-sharp).
  • “brightness”: overall exposure (score 0–100, where 0 = extremely underexposed/dark, 100 = extremely overexposed/bright, 50 = ideal).
  • “contrast”: difference between dark and light regions (score 0–100, where 0 = extremely flat/low contrast, 100 = extremely high contrast).
  • “color_accuracy”: how true the colors appear (score 0–100, where 0 = wildly off, 100 = perfectly natural/accurate).
  • “resolution”: perceived detail level relative to the displayed size (score 0–100, where 0 = very low resolution, 100 = extremely high).
  
For each dimension, besides the numeric score, include a brief `"comment"` field (one or two short sentences) describing why you assigned that score.

**Instructions**:
1. Analyze the provided image (no additional text).
2. Output **only** valid JSON, with this structure:

```json
{
  "noise":        { "score": <0–100> },
  "sharpness":    { "score": <0–100> },
  "brightness":   { "score": <0–100> },
  "contrast":     { "score": <0–100> },
  "color_accuracy": { "score": <0–100> },
  "resolution":   { "score": <0–100> }
}
"""

# Apply chat template
formatted_prompt = apply_chat_template(
    processor, config, prompt, num_images=len(image)
)

# Generate output
output = generate(model, processor, formatted_prompt, image, temperature=0.0, max_tokens=1024, verbose=False)
print(output)
