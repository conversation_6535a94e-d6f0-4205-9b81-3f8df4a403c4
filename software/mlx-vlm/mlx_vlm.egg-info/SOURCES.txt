LICENSE
MANIFEST.in
README.md
requirements.txt
setup.py
mlx_vlm/__init__.py
mlx_vlm/chat.py
mlx_vlm/chat_ui.py
mlx_vlm/convert.py
mlx_vlm/generate.py
mlx_vlm/lora.py
mlx_vlm/prompt_utils.py
mlx_vlm/sample_utils.py
mlx_vlm/tokenizer_utils.py
mlx_vlm/utils.py
mlx_vlm/version.py
mlx_vlm/video_generate.py
mlx_vlm.egg-info/PKG-INFO
mlx_vlm.egg-info/SOURCES.txt
mlx_vlm.egg-info/dependency_links.txt
mlx_vlm.egg-info/entry_points.txt
mlx_vlm.egg-info/requires.txt
mlx_vlm.egg-info/top_level.txt
mlx_vlm/models/__init__.py
mlx_vlm/models/base.py
mlx_vlm/models/switch_layers.py
mlx_vlm/models/deepseek_vl_v2/__init__.py
mlx_vlm/models/deepseek_vl_v2/conversation.py
mlx_vlm/models/deepseek_vl_v2/deepseek_vl_v2.py
mlx_vlm/models/deepseek_vl_v2/language.py
mlx_vlm/models/deepseek_vl_v2/processing_deepsek_vl_v2.py
mlx_vlm/models/deepseek_vl_v2/vision.py
mlx_vlm/models/fastvlm/__init__.py
mlx_vlm/models/fastvlm/fastvlm.py
mlx_vlm/models/fastvlm/language.py
mlx_vlm/models/florence2/__init__.py
mlx_vlm/models/florence2/florence2.py
mlx_vlm/models/florence2/language.py
mlx_vlm/models/florence2/vision.py
mlx_vlm/models/idefics2/__init__.py
mlx_vlm/models/idefics2/idefics2.py
mlx_vlm/models/idefics2/language.py
mlx_vlm/models/idefics2/vision.py
mlx_vlm/models/idefics3/__init__.py
mlx_vlm/models/idefics3/idefics3.py
mlx_vlm/models/idefics3/language.py
mlx_vlm/models/idefics3/vision.py
mlx_vlm/models/llava/__init__.py
mlx_vlm/models/llava/language.py
mlx_vlm/models/llava/llava.py
mlx_vlm/models/llava/vision.py
mlx_vlm/models/llava_bunny/__init__.py
mlx_vlm/models/llava_bunny/language.py
mlx_vlm/models/llava_bunny/llava_bunny.py
mlx_vlm/models/llava_bunny/vision.py
mlx_vlm/models/llava_next/__init__.py
mlx_vlm/models/llava_next/language.py
mlx_vlm/models/llava_next/llava_next.py
mlx_vlm/models/llava_next/vision.py
mlx_vlm/models/mllama/__init__.py
mlx_vlm/models/mllama/language.py
mlx_vlm/models/mllama/mllama.py
mlx_vlm/models/mllama/vision.py
mlx_vlm/models/molmo/__init__.py
mlx_vlm/models/molmo/language.py
mlx_vlm/models/molmo/molmo.py
mlx_vlm/models/molmo/vision.py
mlx_vlm/models/multi_modality/__init__.py
mlx_vlm/models/multi_modality/language.py
mlx_vlm/models/multi_modality/multi_modality.py
mlx_vlm/models/multi_modality/sam.py
mlx_vlm/models/multi_modality/vision.py
mlx_vlm/models/paligemma/__init__.py
mlx_vlm/models/paligemma/language.py
mlx_vlm/models/paligemma/paligemma.py
mlx_vlm/models/paligemma/vision.py
mlx_vlm/models/phi3_v/__init__.py
mlx_vlm/models/phi3_v/language.py
mlx_vlm/models/phi3_v/phi3_v.py
mlx_vlm/models/phi3_v/su_rope.py
mlx_vlm/models/phi3_v/vision.py
mlx_vlm/models/pixtral/__init__.py
mlx_vlm/models/pixtral/language.py
mlx_vlm/models/pixtral/pixtral.py
mlx_vlm/models/pixtral/vision.py
mlx_vlm/models/qwen2_5_vl/__init__.py
mlx_vlm/models/qwen2_5_vl/language.py
mlx_vlm/models/qwen2_5_vl/qwen2_5_vl.py
mlx_vlm/models/qwen2_5_vl/vision.py
mlx_vlm/models/qwen2_vl/__init__.py
mlx_vlm/models/qwen2_vl/language.py
mlx_vlm/models/qwen2_vl/qwen2_vl.py
mlx_vlm/models/qwen2_vl/vision.py
mlx_vlm/tests/test_models.py
mlx_vlm/tests/test_smoke.py
mlx_vlm/tests/test_trainer.py
mlx_vlm/tests/test_trainer_utils.py
mlx_vlm/tests/test_utils.py
mlx_vlm/trainer/__init__.py
mlx_vlm/trainer/lora.py
mlx_vlm/trainer/trainer.py
mlx_vlm/trainer/utils.py