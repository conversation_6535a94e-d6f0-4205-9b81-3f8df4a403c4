import mlx.core as mx
from mlx_vlm import load, generate
from mlx_vlm.prompt_utils import apply_chat_template
from mlx_vlm.utils import load_config

# Load the model
model_path = "llava-fastvithd_1.5b_stage3_llm.int8"
model, processor = load(model_path)
config = load_config(model_path)

# Prepare input
image = ["images/roi_4_std50.jpg"]
prompt = """
You are a Vision‑Language Model tasked with evaluating the technical quality of an input image. Follow these steps EXACTLY:

1. **Noise Analysis**  
   - Examine the image for grain, speckles, or chroma noise artifacts.  
   - Explain in 2–3 sentences: e.g. “I see moderate luminance noise in flat areas, especially in shadows. The grain is uniform and slightly colored.”  
   - Based on your explanation, assign a noise score from 0 (extremely noisy) to 100 (virtually noise‑free).

2. **Color Analysis**  
   - Assess color accuracy, saturation, and balance. Look for color casts, oversaturation, or desaturation.  
   - Explain in 2–3 sentences: e.g. “Colors appear slightly warm, with skin tones leaning orange; saturation is strong but not clipped.”  
   - Assign a color score from 0 (poor color) to 100 (perfectly balanced, accurate color).

3. **Sharpness Analysis**  
   - Judge edge definition, detail clarity, and presence of blur (motion or focus).  
   - Explain in 2–3 sentences: e.g. “Fine details are clear, edges are crisp; minor softness around the background indicates slight defocus.”  
   - Assign a sharpness score from 0 (entirely blurry) to 100 (razor‑sharp everywhere).

4. **Contrast Analysis**  
   - Evaluate the tonal range and separation between highlights and shadows.  
   - Explain in 2–3 sentences: e.g. “The image has strong midtone contrast but slightly clipped highlights in bright areas.”  
   - Assign a contrast score from 0 (flat, no contrast) to 100 (excellent tonal separation).

5. **JSON Summary**  
   - After you’ve given explanations and scores for each aspect, output ONLY a JSON object summarizing the scores, in this exact format:
   ```json
   {
     "noise": <noise_score>,
     "color": <color_score>,
     "sharpness": <sharpness_score>,
     "contrast": <contrast_score>
   }
"""

# Apply chat template
formatted_prompt = apply_chat_template(
    processor, config, prompt, num_images=len(image)
)

# Generate output
output = generate(model, processor, formatted_prompt, image, temperature=0.0, max_tokens=10240, verbose=False)
print(output)


# ,
#   "color_accuracy": <>,
#   "resolution": <>

# put answer in <>:

# 1. Analyze the provided image, evaluate its quality along these six dimensions: noise, sharpness, brightness, contrast, color_accuracy, resolution.
# 2. For each dimension, assign a score from 0 to 100.
# 3. Output score in valid JSON format, with this structure, answer in <>:
# {
#   "noise": <>,
#   "sharpness": <>,
#   "brightness": <>,
#   "contrast": <>
# }


#### detailed on  image = ["images/roi_4_std25.jpg"]
# prompt = """
# Could you help analyze the provided image qualtiy in terms of noise, sharpness, brightness, and contrast, output score 0 to 100 with below structure:
# noise:
# sharpness:
# brightness:
# contrast:
# You answer for provide image is: 
# """


# image = ["images/roi_4_std25.jpg"]
# prompt = """
# Could you help analyze the provided image qualtiy in terms of noise, sharpness, brightness, and contrast, output score 0 to 100 with below structure:
# noise: %d
# sharpness: %d
# brightness: %d
# contrast: %d
# """