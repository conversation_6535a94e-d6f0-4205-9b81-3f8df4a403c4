import cv2
import numpy as np

def add_gaussian_noise(input_image, mean=0, std=25, output_path=None):
    """
    Add Gaussian noise to an image (supports both file paths and image arrays).
    
    Args:
        input_image: Can be either a file path (str) or a numpy array
        mean: Mean of Gaussian noise
        std: Standard deviation of noise intensity
        output_path: Optional path to save noisy image
    """
    # Load image if input is a file path
    if isinstance(input_image, str):
        image = cv2.imread(input_image)
        if image is None:
            raise ValueError(f"Could not load image from {input_image}")
    else:
        image = input_image.copy()
    
    # Add Gaussian noise
    image_float = image.astype(np.float32)
    noise = np.random.normal(mean, std, image.shape).astype(np.float32)
    noisy_image = cv2.add(image_float, noise)
    noisy_image = np.clip(noisy_image, 0, 255).astype(np.uint8)
    
    # Save if output path is provided
    if output_path:
        cv2.imwrite(output_path, noisy_image)
    
    return noisy_image

def crop_roi(input_image, x, y, w, h, output_path=None):
    """
    Crop a region of interest from an image (supports both file paths and image arrays).
    
    Args:
        input_image: Can be either a file path (str) or a numpy array
        x, y: Top-left coordinates of ROI
        w, h: Width and height of ROI
        output_path: Optional path to save cropped image
    """
    # Load image if input is a file path
    if isinstance(input_image, str):
        image = cv2.imread(input_image)
        if image is None:
            raise ValueError(f"Could not load image from {input_image}")
    else:
        image = input_image.copy()
    
    # Validate coordinates
    height, width = image.shape[:2]
    x = max(0, x)
    y = max(0, y)
    x_end = min(x + w, width)
    y_end = min(y + h, height)
    
    # Perform cropping
    cropped = image[y:y_end, x:x_end]
    
    # Save if output path is provided
    if output_path:
        cv2.imwrite(output_path, cropped)
    
    return cropped

# Example usage
if __name__ == "__main__":
    # Process directly from file to file
    noisy_img = add_gaussian_noise("input.jpg", std=35, output_path="noisy_output.jpg")
    cropped_img = crop_roi("noisy_output.jpg", 100, 50, 200, 150, output_path="cropped_output.jpg")

    # Or work with in-memory images
    img_array = cv2.imread("input.jpg")
    noisy_array = add_gaussian_noise(img_array, std=35)
    cropped_array = crop_roi(noisy_array, 100, 50, 200, 150)
    cv2.imwrite("final_result.jpg", cropped_array)
