#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本，用于验证conda环境是否正常工作
"""

import os
import sys
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("Test Runner")

logger.info("开始测试")
logger.info(f"Python 版本: {sys.version}")
logger.info(f"当前工作目录: {os.getcwd()}")

try:
    import cv2
    logger.info(f"OpenCV 已安装: {cv2.__version__}")
except ImportError:
    logger.error("Error: OpenCV (cv2) 未安装")

try:
    import numpy as np
    logger.info(f"NumPy 已安装: {np.__version__}")
except ImportError:
    logger.error("Error: NumPy 未安装")

try:
    import torch
    logger.info(f"PyTorch 已安装: {torch.__version__}")
except ImportError:
    logger.warning("Warning: PyTorch 未安装")

try:
    import paddle
    logger.info(f"PaddlePaddle 已安装: {paddle.__version__}")
except ImportError:
    logger.warning("Warning: PaddlePaddle 未安装")

# 测试目录访问
INPUT_DIR = "/home/<USER>/T2/data/bokeh_test"
if os.path.exists(INPUT_DIR):
    logger.info(f"输入目录存在: {INPUT_DIR}")
    files = os.listdir(INPUT_DIR)
    logger.info(f"目录中有 {len(files)} 个文件")
    for file in files[:5]:  # 只显示前5个文件
        logger.info(f"文件: {file}")
else:
    logger.error(f"输入目录不存在: {INPUT_DIR}")

logger.info("测试完成")
