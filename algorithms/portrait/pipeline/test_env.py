#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to check if the conda environment has all required packages
"""

import os
import sys

print("Python version:", sys.version)
print("Current working directory:", os.getcwd())

try:
    import cv2
    print("OpenCV is available, version:", cv2.__version__)
except ImportError:
    print("Error: OpenCV (cv2) is not available")

try:
    import numpy as np
    print("NumPy is available, version:", np.__version__)
except ImportError:
    print("Error: NumPy is not available")

try:
    import torch
    print("PyTorch is available, version:", torch.__version__)
except ImportError:
    print("Error: PyTorch is not available")

try:
    import paddle
    print("PaddlePaddle is available, version:", paddle.__version__)
except ImportError:
    print("Error: PaddlePaddle is not available")

print("Test completed")
