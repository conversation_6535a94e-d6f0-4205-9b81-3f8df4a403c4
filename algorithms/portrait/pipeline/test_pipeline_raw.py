#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the simplified portrait pipeline with intermediate outputs
"""

import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_raw import SimplifiedPortraitPipeline

def test_single_image():
    """Test processing a single image with intermediate outputs"""
    
    # Test image path (adjust as needed)
    test_image = "/home/<USER>/T2/data/bokeh_test/test_image.jpg"
    output_dir = "/home/<USER>/T2/data/pipeline_raw_test"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Check if test image exists
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        print("Please update the test_image path in the script")
        return
    
    print("="*60)
    print("Testing Simplified Portrait Pipeline")
    print("="*60)
    
    try:
        # Initialize pipeline
        print("Initializing pipeline...")
        pipeline = SimplifiedPortraitPipeline()
        
        # Process image
        print(f"Processing: {test_image}")
        result = pipeline.process_image(
            image_path=test_image,
            output_path=os.path.join(output_dir, "bokeh_result.jpg"),
            save_intermediates=True,
            intermediate_dir=os.path.join(output_dir, "intermediates")
        )
        
        # Print results
        print("\n" + "="*60)
        print("PROCESSING RESULTS")
        print("="*60)
        print(f"✓ Success: {result['success']}")
        print(f"✓ Processing time: {result['processing_time']:.2f} seconds")
        print(f"✓ Focus depth: {result['focus_depth']:.3f}")
        
        if result['face_box'] is not None:
            x1, y1, x2, y2 = result['face_box']
            print(f"✓ Face detected at: [{x1}, {y1}, {x2}, {y2}]")
            print(f"  Face size: {x2-x1} x {y2-y1} pixels")
        else:
            print("⚠ No face detected")
        
        print(f"✓ Portrait mask shape: {result['portrait_mask'].shape}")
        print(f"✓ Depth map shape: {result['depth_map'].shape}")
        print(f"✓ Bokeh image shape: {result['bokeh_image'].shape}")
        
        if result['intermediate_dir']:
            print(f"✓ Intermediate files saved to: {result['intermediate_dir']}")
            
            # List intermediate files
            intermediate_files = list(Path(result['intermediate_dir']).glob("*"))
            print(f"✓ Generated {len(intermediate_files)} intermediate files:")
            for file in sorted(intermediate_files):
                print(f"  - {file.name}")
        
        print("\n" + "="*60)
        print("OUTPUT FILES")
        print("="*60)
        print(f"Final result: {os.path.join(output_dir, 'bokeh_result.jpg')}")
        
        if result['intermediate_dir']:
            print(f"Intermediate files:")
            print(f"  - Face detection: {os.path.basename(test_image).split('.')[0]}_face_detection.jpg")
            print(f"  - Portrait mask: {os.path.basename(test_image).split('.')[0]}_portrait_mask.jpg")
            print(f"  - Portrait overlay: {os.path.basename(test_image).split('.')[0]}_portrait_overlay.jpg")
            print(f"  - Depth map (gray): {os.path.basename(test_image).split('.')[0]}_depth_map.jpg")
            print(f"  - Depth map (color): {os.path.basename(test_image).split('.')[0]}_depth_colored.jpg")
            print(f"  - Combined view: {os.path.basename(test_image).split('.')[0]}_combined_visualization.jpg")
        
        print("\n✓ Test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

def test_batch_processing():
    """Test batch processing with intermediate outputs"""
    
    # Test directory path (adjust as needed)
    test_dir = "/home/<USER>/T2/data/bokeh_test"
    output_dir = "/home/<USER>/T2/data/pipeline_raw_batch_test"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Check if test directory exists
    if not os.path.exists(test_dir):
        print(f"Test directory not found: {test_dir}")
        print("Please update the test_dir path in the script")
        return
    
    print("="*60)
    print("Testing Batch Processing")
    print("="*60)
    
    try:
        # Initialize pipeline
        print("Initializing pipeline...")
        pipeline = SimplifiedPortraitPipeline()
        
        # Find test images
        test_images = list(Path(test_dir).glob("*.jpg")) + list(Path(test_dir).glob("*.png"))
        
        if not test_images:
            print(f"No images found in {test_dir}")
            return
        
        print(f"Found {len(test_images)} images to process")
        
        # Process each image
        for i, image_path in enumerate(test_images[:3]):  # Limit to first 3 images for testing
            print(f"\n[{i+1}/{min(3, len(test_images))}] Processing: {image_path.name}")
            
            result = pipeline.process_image(
                image_path=str(image_path),
                output_path=os.path.join(output_dir, f"bokeh_{image_path.name}"),
                save_intermediates=True,
                intermediate_dir=os.path.join(output_dir, "intermediates")
            )
            
            print(f"  ✓ Time: {result['processing_time']:.2f}s")
            print(f"  ✓ Face: {'Yes' if result['face_box'] is not None else 'No'}")
            print(f"  ✓ Focus depth: {result['focus_depth']:.3f}")
        
        print(f"\n✓ Batch processing completed!")
        
    except Exception as e:
        print(f"\n❌ Batch test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Simplified Portrait Pipeline')
    parser.add_argument('--mode', choices=['single', 'batch'], default='single',
                       help='Test mode: single image or batch processing')
    
    args = parser.parse_args()
    
    if args.mode == 'single':
        test_single_image()
    elif args.mode == 'batch':
        test_batch_processing()
