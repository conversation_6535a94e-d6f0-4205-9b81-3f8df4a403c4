# parameters
nc: 1  # number of classes
depth_multiple: 0.33  # model depth multiple
width_multiple: 0.50  # layer channel multiple

# anchors Retinaface
anchors:
  - [19.2,16, 24.19,20.16, 30.48,25.40]  # P3/8
  - [38.40,32, 48.38,40.32, 60.96,50.80]  # P4/16_
  - [76.8,64, 96.76,80.63, 121.91,101.59]  # P5/32

# YOLOv5 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, <PERSON>, [64, 3]],  # 0-P1/2
   [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
   [-1, 3, C3, [128]],
   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
   [-1, 9, C3, [256]],
   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
   [-1, 9, C3, [512]],
   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
   [-1, 1, SPP, [1024, [5, 9, 13]]],
   [-1, 3, C3<PERSON><PERSON><PERSON>, [1024, False]],  # 9
  ]

# YOLOv5 v6.0 head
head:
  [[-1, 1, Conv, [512, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 6], 1, Concat, [1]],  # cat backbone P4
   [-1, 3, C3, [512, False]],  # 13 ---> rf 15

   [-1, 1, Conv, [256, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 4], 1, Concat, [1]],  # cat backbone P3
   [-1, 3, C3, [256, False]],  # 17 ---> rf 7

   [-1, 1, Conv, [128, 1, 1 ]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[ -1, 2], 1, Concat, [1]],  # cat backbone P3
   [-1, 3, C3, [128, False]],  # 21 (P3/8-small) ---> rf 3

   [-1, 1, Conv, [128, 3, 2]],
   [[-1, 18], 1, Concat, [1]],  # cat head P4
   [-1, 3, C3, [256, False]],  # 24 (P4/16-medium) ---> rf 7

   [-1, 1, Conv, [256, 3, 2]],
   [[-1, 14], 1, Concat, [1]],  # cat head P5
   [-1, 3, C3, [512, False]],  # 27 (P5/32-large) ---> rf 15

   [21, 1, MultiSEAM, [128, 1, 3, [6, 7, 8], 16]],
   [-1, 1, Conv, [256, 1, 1]],  # 29
   [24, 1, MultiSEAM, [256, 1, 3, [6, 7, 8], 16]],
   [-1, 1, Conv, [512, 1, 1]],  # 31
   [27, 1, MultiSEAM, [512, 1, 3, [6, 7, 8], 16]],
   [-1, 1, Conv, [1024, 1, 1]],  # 33

   [[29, 31, 33], 1, Detect, [nc, anchors]],  # Detect(P3, P4, P5)
  ]
