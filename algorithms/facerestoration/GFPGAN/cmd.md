
# 环境变量设置
export BASICSR_JIT='True'
pip install basicsr
pip install facexlib
python setup.py develop
pip install realesrgan
pip install Ninja

# lock
~/.cache/torch_extensions


# train
python3 gfpgan/train.py -opt options/train_gfpgan_v1_train1.yml
python3 gfpgan/train.py -opt options/train_gfpgan_v1_trainretouch.yml


#  test
python3 inference_gfpgan.py -i inputs/whole_imgs -o results -v 1 -s 1
python3 inference_gfpgan.py -i inputs/whole_imgs -o results -v train1.net_g_800000 -s 1
python3 inference_gfpgan.py -i inputs/whole_imgs -o results -v train1.net_g_800000_r -s 1