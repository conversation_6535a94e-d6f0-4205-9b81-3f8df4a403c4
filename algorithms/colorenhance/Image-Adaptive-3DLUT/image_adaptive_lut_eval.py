import argparse
import time
import torch
from torchvision.utils import save_image
from torch.utils.data import DataLoader
from torch.autograd import Variable

from models_x import *
from datasets_eval import *


parser = argparse.ArgumentParser()
parser.add_argument("--dataset_path", type=str, default="/home/<USER>/data/imagesresults/sef", help="name of the dataset")
parser.add_argument("--input_color_space", type=str, default="sRGB", help="input color space: sRGB or XYZ")
parser.add_argument("--model_dir", type=str, default="/home/<USER>/code/IEDemo/algorithms/colorenhance/Image-Adaptive-3DLUT/pretrained_models", help="directory of saved models")
parser.add_argument("--output_dir", type=str, default="/home/<USER>/data/imagesresults/sef2/", help="directory of output images")
parser.add_argument("--tag", type=str, default="-3dlut", help="tag for output images")
opt = parser.parse_args()
opt.model_dir = os.path.join(opt.model_dir, opt.input_color_space)

# use gpu when detect cuda
cuda = True if torch.cuda.is_available() else False
# Tensor type
Tensor = torch.cuda.FloatTensor if cuda else torch.FloatTensor

criterion_pixelwise = torch.nn.MSELoss()
LUT0 = Generator3DLUT_identity()
LUT1 = Generator3DLUT_zero()
LUT2 = Generator3DLUT_zero()
#LUT3 = Generator3DLUT_zero()
#LUT4 = Generator3DLUT_zero()
classifier = Classifier()
trilinear_ = TrilinearInterpolation() 

if cuda:
    LUT0 = LUT0.cuda()
    LUT1 = LUT1.cuda()
    LUT2 = LUT2.cuda()
    #LUT3 = LUT3.cuda()
    #LUT4 = LUT4.cuda()
    classifier = classifier.cuda()
    criterion_pixelwise.cuda()

# Load pretrained models
LUTs = torch.load("%s/LUTs.pth" % (opt.model_dir))
LUT0.load_state_dict(LUTs["0"])
LUT1.load_state_dict(LUTs["1"])
LUT2.load_state_dict(LUTs["2"])
#LUT3.load_state_dict(LUTs["3"])
#LUT4.load_state_dict(LUTs["4"])
LUT0.eval()
LUT1.eval()
LUT2.eval()
#LUT3.eval()
#LUT4.eval()
classifier.load_state_dict(torch.load("%s/classifier.pth" % (opt.model_dir)))
classifier.eval()

dataloader = DataLoader(
    ImageDataset_sRGB("%s" % opt.dataset_path,  mode="test"),
    batch_size=1,
    shuffle=False,
    num_workers=1,
)

def generator(img):

    pred = classifier(img).squeeze()
    
    LUT = pred[0] * LUT0.LUT + pred[1] * LUT1.LUT + pred[2] * LUT2.LUT #+ pred[3] * LUT3.LUT + pred[4] * LUT4.LUT

    combine_A = img.new(img.size())
    _, combine_A = trilinear_(LUT,img)

    return combine_A


def visualize_result():
    """Saves a generated sample from the validation set"""
    out_dir = opt.output_dir
    os.makedirs(out_dir, exist_ok=True)
    for i, batch in enumerate(dataloader):
        real_A = Variable(batch["A_input"].type(Tensor))
        img_name = batch["input_name"]
        fake_B = generator(real_A)

        #real_B = Variable(batch["A_exptC"].type(Tensor))
        #img_sample = torch.cat((real_A.data, fake_B.data, real_B.data), -1)
        #save_image(img_sample, "images/LUTs/paired/JPGsRGB8_to_JPGsRGB8_WB_original_5LUT/%s.png" % (img_name[0][:-4]), nrow=3, normalize=False)
        save_image(fake_B, os.path.join(out_dir,"%s%s.png" % (img_name[0][:-4], opt.tag)), nrow=1, normalize=False)
        print(f"processing {i+1}/{len(dataloader)}, {img_name[0]}, saved to {os.path.join(out_dir,img_name[0][:-4]+opt.tag+'.png')}")

# ----------
#  evaluation
# ----------
visualize_result()

