import glob
import random
import os
import numpy as np
import torch
import cv2

from torch.utils.data import Dataset
from PIL import Image
import torchvision.transforms as transforms
import torchvision.transforms.functional as TF
import torchvision_x_functional as TF_x


class ImageDataset_sRGB(Dataset):
    def __init__(self, root, mode="test"):

        # find all input images in the root directory
        self.test_input_files = sorted(glob.glob(os.path.join(root , "*.png")))
        self.test_input_files += sorted(glob.glob(os.path.join(root, "*.jpg")))
        self.test_input_files += sorted(glob.glob(os.path.join(root, "*.jpeg"))) 
        print("Found %d input images in %s" % (len(self.test_input_files), root))

    def __getitem__(self, index):
        
        # print("Loading input image: %s" % self.test_input_files[index % len(self.test_input_files)])
        img_name = os.path.split(self.test_input_files[index % len(self.test_input_files)])[-1]
        img_input = Image.open(self.test_input_files[index % len(self.test_input_files)])


        img_input = TF.to_tensor(img_input)

        return {"A_input": img_input, "input_name": img_name}

    def __len__(self):
        return len(self.test_input_files)
