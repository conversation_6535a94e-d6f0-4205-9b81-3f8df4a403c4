%% Uninstall
%
% Copyright (c) 2018-present, <PERSON><PERSON><PERSON>
% York University, Canada
% <EMAIL> | <EMAIL>
%
% This source code is licensed under the license found in the
% LICENSE file in the root directory of this source tree.
% All rights reserved.
%
% Please cite the following work if this program is used:
% <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, 
% "When color constancy goes wrong: Correcting improperly white-balanced 
% images", CVPR 2019.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% 

disp('Uninstalling WB for sRGB images...')
current = pwd;
rmpath(fullfile(current,'classes'));
savepath
disp('Done!');
