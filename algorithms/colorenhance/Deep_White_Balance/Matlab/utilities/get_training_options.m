%% training options
% Author: <PERSON><PERSON><PERSON>
% Copyright (c) 2019 Samsung Electronics Co., Ltd. All Rights Reserved
% Please cite our paper:
% <PERSON><PERSON><PERSON> and <PERSON>. Deep White-Balance Editing. In CVPR, 2020.
%%

function options = get_training_options(epochs,miniBatch,lR,...
    checkpoint_dir,validation_data,GPUDevice, L2Reg, checkpoint_period)

if nargin == 6
    L2Reg = 1.000e-7;
    checkpoint_period = 5;
elseif nargin == 7
    checkpoint_period = 5;
end
gpuDevice(GPUDevice);

if exist(checkpoint_dir,'dir') == 0
    mkdir(checkpoint_dir);
end

options = trainingOptions('adam', ...
    'InitialLearnRate',lR, ...
    'L2Regularization',L2Reg, ...
    'MaxEpochs',epochs, ...
    'MiniBatchSize',miniBatch, ...
    'CheckpointPath',checkpoint_dir, ...
    'Shuffle','once',...
    'VerboseFrequency',1,...
    'LearnRateSchedule','piecewise',...
    'LearnRateDropPeriod',50,...
    'LearnRateDropFactor',0.5,...
    'Plots','none',...
    'ExecutionEnvironment','gpu',...
    'GradientDecayFactor',0.9,... 
    'SquaredGradientDecayFactor' ,0.999,... 
    'ValidationData',validation_data,...
    'ValidationFrequency',1500 * 4, ...
    'OutputFcn',@(info)plotTrainingAccuracy(info,checkpoint_dir,...
    checkpoint_period));
