% Copyright 2017 Google Inc.
%
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
%
%      http://www.apache.org/licenses/LICENSE-2.0
%
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function paths = DataPaths

% Where the Gehler-Shi dataset and its preprocessed variants are stored.
% paths.gehler_shi = '/usr/local/google/home/<USER>/code/ffcc/data/shi_gehler/';
paths.gehler_shi = '/home/<USER>/Project/AutoWhiteBalance/ffcc/data/shi_gehler';

% Where the <PERSON> et al dataset and its preprocessed variants are stored.
paths.cheng = '';
