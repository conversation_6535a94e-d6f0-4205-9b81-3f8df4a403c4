% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengPanasonicGX1ThumbHyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:23:540.

params.HYPERPARAMS = struct( ...
'CROSSENT_MULTIPLIER', 2^-3.25, ...
'VONMISES_MULTIPLIER', 2^-2.25, ...
'FILTER_MULTIPLIERS', [2^-21.75, 2^-26.5], ...
'BIAS_MULTIPLIER', 2^-26.25, ...
'FILTER_SHIFTS', [2^-43.5, 2^-36], ...
'BIAS_SHIFT', 2^-46.75, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.16704, ...
    'mean2', 3.14218, ...
    'mean4', 4.98445, ...
    'median', 1.27871, ...
    'tri', 1.49956, ...
    'b25', 0.406977, ...
    'w25', 5.25321, ...
    'w05', 9.4975, ...
    'max', 13.5407 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 3.02997, ...
    'mean2', 4.33692, ...
    'mean4', 6.89781, ...
    'median', 1.83852, ...
    'tri', 2.14384, ...
    'b25', 0.643363, ...
    'w25', 7.20886, ...
    'w05', 13.081, ...
    'max', 18.2859 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 3.1134, ...
    'mean2', 6.26905, ...
    'mean4', 16.7865, ...
    'median', 1.93542, ...
    'tri', 1.98154, ...
    'b25', 0.831099, ...
    'w25', 7.72484, ...
    'w05', 20.2196, ...
    'max', 61.4341 ...
)    , ...
'uv_bin_bias', [-0.136013, 0.183179], ...
'final_losses', [52.7249, 57.6171, 54.985], ...
'train_times', [165.109, 179.641, 209.036], ...
'min_feature_time', 0.001778, ...
'min_filter_time', 0.000823, ...
'median_feature_time', 0.013224, ...
'median_filter_time', 0.005253, ...
'opt_traces', ...
  {{[120.653, 77.7824, 64.3132, 60.837, 55.8613, 54.5139, 52.4851, 50.1059, 49.5882, 48.1702, 47.689, 46.9192, 46.2127, 45.6649, 45.3391, 45.1076, 44.9071], [118.03, 80.2434, 66.2938, 62.8737, 58.2634, 56.6273, 54.7405, 52.4271, 51.4464, 49.4423, 48.5673, 47.7126, 46.5018, 46.2683, 45.668, 45.4799, 45.2655], [116.282, 76.7706, 63.6355, 59.8595, 56.1072, 54.5846, 52.6523, 49.7295, 49.3444, 46.7586, 46.1306, 45.2596, 44.6195, 44.111, 43.7637, 43.5699, 43.3399]; ...
    [69.1644, 67.4057, 66.175, 64.6089, 64.1488, 63.9339, 62.4512, 61.0454, 60.268, 59.9454, 59.4776, 59.0273, 58.5585, 58.2574, 58.1284, 57.9907, 57.8536, 57.2447, 56.8996, 56.7243, 56.5755, 56.4377, 56.2998, 56.108, 55.8704, 55.4114, 55.1186, 54.8769, 54.7986, 54.6995, 54.6707, 54.596, 54.4153, 54.2499, 54.1491, 53.9693, 53.8467, 53.6725, 53.5552, 53.491, 53.4644, 53.4232, 53.3313, 53.2606, 53.2233, 53.1963, 53.1648, 53.1254, 53.0781, 53.0408, 52.9958, 52.9567, 52.9246, 52.9141, 52.9015, 52.8901, 52.8703, 52.8627, 52.8406, 52.813, 52.7864, 52.7692, 52.7615, 52.7519, 52.7249], [76.8185, 75.2499, 73.9942, 71.6474, 70.744, 69.9661, 69.2568, 68.5778, 67.9906, 67.1018, 66.9319, 66.3942, 66.2438, 66.0959, 65.4044, 64.6365, 63.8485, 63.3456, 63.1763, 62.991, 62.7773, 62.4582, 61.9545, 61.7567, 61.4671, 61.3606, 61.1909, 61.0655, 60.6403, 60.3072, 60.0819, 59.8984, 59.76, 59.6024, 59.4879, 59.2944, 59.114, 58.9638, 58.8589, 58.7585, 58.6498, 58.4777, 58.3757, 58.259, 58.1883, 58.1713, 58.1572, 58.1398, 58.1154, 58.0749, 57.9869, 57.885, 57.8368, 57.8151, 57.8085, 57.7996, 57.7865, 57.7663, 57.7357, 57.692, 57.658, 57.6435, 57.6301, 57.6266, 57.6171], [68.6512, 67.4069, 66.7168, 66.29, 65.5178, 64.6385, 63.9688, 63.7232, 63.3896, 62.9615, 62.7561, 62.471, 62.2511, 61.9891, 61.4774, 60.581, 60.3341, 60.088, 59.9376, 59.7041, 59.3829, 59.0108, 58.6905, 58.5436, 58.2201, 57.7858, 57.4769, 57.2851, 57.192, 57.0693, 56.8538, 56.5948, 56.4388, 56.318, 56.2279, 56.1112, 55.9141, 55.8212, 55.7361, 55.6404, 55.5573, 55.4326, 55.3751, 55.311, 55.2512, 55.2095, 55.1947, 55.179, 55.1461, 55.1094, 55.0878, 55.0759, 55.069, 55.057, 55.0414, 55.0331, 55.0268, 55.0204, 55.0154, 55.0095, 55.0049, 55.0019, 54.9965, 54.9893, 54.985]}} ...
);

% Tuning error = 1.547815
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-06-04, 21:30:57:531
