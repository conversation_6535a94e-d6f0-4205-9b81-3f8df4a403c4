function [params, metrics] = G<PERSON>lerShiDeepEHyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-08-09, 09:35:47:484.

params.HYPERPARAMS = struct( ...
'WHITEN_VARIANCE_PAD', 2^-1.5, ...
'RANDOM_INIT_SIGMA', 2^-10, ...
'WEIGHT_DECAY', [2^-4.5, 2^1], ...
'VONMISES_MULTIPLIER', 2^1.5, ...
'CROSSENT_MULTIPLIER', 2^-9, ...
'FILTER_MULTIPLIERS', [2^-25, 2^-24.75], ...
'BIAS_MULTIPLIER', 2^-23.5, ...
'FILTER_SHIFTS', [2^-38, 2^-32], ...
'BIAS_SHIFT', 2^-44.25, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 1.65724, ...
    'mean2', 2.66873, ...
    'mean4', 4.66671, ...
    'median', 0.821554, ...
    'tri', 1.0028, ...
    'b25', 0.254959, ...
    'w25', 4.4543, ...
    'w05', 8.65715, ...
    'max', 14.0828 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 2.17545, ...
    'mean2', 3.39648, ...
    'mean4', 5.74588, ...
    'median', 1.19237, ...
    'tri', 1.42867, ...
    'b25', 0.353661, ...
    'w25', 5.72959, ...
    'w05', 10.7496, ...
    'max', 16.3509 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 2.10865, ...
    'mean2', 4.79483, ...
    'mean4', 13.896, ...
    'median', 1.08153, ...
    'tri', 1.21188, ...
    'b25', 0.344235, ...
    'w25', 5.79289, ...
    'w05', 15.3569, ...
    'max', 62.5196 ...
)    , ...
'uv_bin_bias', [0.00129346, -0.0498072], ...
'final_losses', [1426.34, 1369.49, 1326.85], ...
'train_times', [102.289, 96.9746, 97.7571], ...
'min_feature_time', 0.016875, ...
'min_filter_time', 0.005149, ...
'median_feature_time', 0.020385, ...
'median_filter_time', 0.00603, ...
'opt_traces', ...
  {{[7314.29, 5857.62, 4979.85, 3861.91, 3313.17, 2881.71, 2618.13, 2416.47, 2290.9, 2236.77, 2125.45, 2066.2, 1999.2, 1972.07, 1922.32, 1893.33, 1851.6, 1810.99, 1783.95, 1762.44, 1746.2, 1731.84, 1715.76, 1695.58, 1681.71, 1671.7, 1652.43, 1634.15, 1622.72, 1606.79, 1596.46, 1586.03, 1576.83, 1565.1, 1550.46, 1542.21, 1532.52, 1528.94, 1524.85, 1519.29, 1511.34, 1506.63, 1501.44, 1495.72, 1490.7, 1484.66, 1479.48, 1476.07, 1471.7, 1468.08, 1464.38, 1459.49, 1456.27, 1451.61, 1448.63, 1445.69, 1442.41, 1439.39, 1437.12, 1436.2, 1433.98, 1431.81, 1430.25, 1428.44, 1426.34], [7825.98, 7251.04, 5636.73, 4138.03, 3143.94, 2710.9, 2366.7, 2178.84, 2090.27, 2018.05, 2004.53, 1931.99, 1883.04, 1865.58, 1823.4, 1814.54, 1795.95, 1767.99, 1727.5, 1697.71, 1682.31, 1667.5, 1646.8, 1628.38, 1605.9, 1583.24, 1568.05, 1560.92, 1552.42, 1542.71, 1524.34, 1515.62, 1507.83, 1501.54, 1493.83, 1485.54, 1479.32, 1473.2, 1466.7, 1458.81, 1450.99, 1446.57, 1442.7, 1439.48, 1435.73, 1433.92, 1430.16, 1425.31, 1419.64, 1415.34, 1411.93, 1408.37, 1404.84, 1401.05, 1397.69, 1395.03, 1392.44, 1389.29, 1385.44, 1382.51, 1379.85, 1377.04, 1375.07, 1372.08, 1369.49], [6667.05, 5213.18, 4211.75, 3580.55, 3483.19, 3003.34, 2731.48, 2343.92, 2253.28, 2157.59, 2077.5, 2027.39, 1929.26, 1898.72, 1867.37, 1817.39, 1793.64, 1757.73, 1733.13, 1710.88, 1676.88, 1650.43, 1628, 1606.69, 1591.91, 1568.44, 1545.27, 1535.03, 1522.9, 1515.63, 1509.07, 1497.74, 1486.11, 1478.17, 1471.8, 1463.65, 1451.79, 1439.61, 1431.84, 1423.89, 1416.23, 1409.78, 1404.36, 1397.18, 1391, 1384.9, 1380.67, 1376.17, 1370.27, 1365.17, 1361.46, 1359.49, 1356.53, 1353.55, 1349.58, 1346.91, 1344.03, 1341.45, 1339.11, 1336.3, 1334.16, 1332.31, 1330.67, 1328.39, 1326.85]}} ...
);

% Tuning error = 1.091683
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-08-09, 09:41:03:892
