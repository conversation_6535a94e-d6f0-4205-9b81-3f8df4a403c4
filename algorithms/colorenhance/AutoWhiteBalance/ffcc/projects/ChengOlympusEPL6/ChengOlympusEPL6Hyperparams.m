% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengOlympusEPL6Hyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:22:125.

params.HYPERPARAMS = struct( ...
'VONMISES_MULTIPLIER', 2^-6, ...
'CROSSENT_MULTIPLIER', 2^-21.5, ...
'FILTER_SHIFTS', [2^-38.5, 2^-35.75], ...
'FILTER_MULTIPLIERS', [2^-30, 2^-34.25], ...
'BIAS_SHIFT', 2^-70.25, ...
'BIAS_MULTIPLIER', 2^-23, ...
'VON_MISES_DIAGONAL_EPS', 2^1.25 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 1.91048, ...
    'mean2', 2.84356, ...
    'mean4', 4.74312, ...
    'median', 1.1419, ...
    'tri', 1.29288, ...
    'b25', 0.350937, ...
    'w25', 4.71953, ...
    'w05', 9.11192, ...
    'max', 13.3201 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 2.80155, ...
    'mean2', 4.19742, ...
    'mean4', 7.11077, ...
    'median', 1.63819, ...
    'tri', 1.87089, ...
    'b25', 0.548983, ...
    'w25', 6.93808, ...
    'w05', 13.7007, ...
    'max', 20.5605 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 1.92219, ...
    'mean2', 5.15674, ...
    'mean4', 17.1617, ...
    'median', 1.04293, ...
    'tri', 1.1669, ...
    'b25', 0.364273, ...
    'w25', 5.10448, ...
    'w05', 14.107, ...
    'max', 65.0746 ...
)    , ...
'uv_bin_bias', [0.00513817, 0.0251741], ...
'final_losses', [3.31728, 3.2919, 3.37493], ...
'train_times', [183.07, 216.831, 185.303], ...
'min_feature_time', 0.066617, ...
'min_filter_time', 0.000782, ...
'median_feature_time', 0.306816, ...
'median_filter_time', 0.0038695, ...
'opt_traces', ...
  {{[0.00039544, 0.000394349, 0.000394349, 0.000394349, 0.000394349, 0.000394349], [0.000387027, 0.000385906, 0.000385906, 0.000385906, 0.000385906, 0.000385906], [0.000384222, 0.000383125, 0.000383125, 0.000383125, 0.000383125]; ...
    [11.1188, 11.1156, 11.0967, 11.0488, 11.0406, 11.0363, 11.0105, 7.66403, 6.75145, 5.57573, 4.90724, 4.21511, 4.0384, 3.96187, 3.86947, 3.75121, 3.60135, 3.5406, 3.50671, 3.4856, 3.46619, 3.41966, 3.39283, 3.37001, 3.35523, 3.34384, 3.33969, 3.33332, 3.32912, 3.32684, 3.3254, 3.32392, 3.32247, 3.32156, 3.32107, 3.32036, 3.3196, 3.31891, 3.31851, 3.31817, 3.31797, 3.31761, 3.31745, 3.31743, 3.31737, 3.31734, 3.31733, 3.31731, 3.31731, 3.3173, 3.3173, 3.3173, 3.3173, 3.3173, 3.3173, 3.3173, 3.31729, 3.31729, 3.31729, 3.31729, 3.31729, 3.31729, 3.31729, 3.31729, 3.31728], [10.816, 10.8112, 10.8052, 10.7938, 10.7851, 10.7838, 10.7803, 10.7784, 10.7711, 7.48437, 5.83941, 5.31248, 4.70579, 4.41801, 4.1363, 4.01879, 3.84076, 3.66537, 3.59289, 3.5506, 3.47215, 3.42176, 3.39097, 3.37146, 3.35784, 3.34198, 3.3338, 3.32815, 3.32367, 3.31899, 3.31643, 3.31508, 3.31347, 3.31121, 3.30918, 3.30755, 3.30635, 3.3052, 3.30423, 3.30228, 3.30125, 3.30055, 3.30007, 3.29937, 3.29811, 3.29734, 3.29653, 3.29591, 3.29547, 3.29495, 3.29459, 3.29416, 3.29388, 3.29345, 3.29316, 3.29294, 3.29284, 3.29277, 3.29269, 3.2926, 3.29249, 3.2923, 3.29211, 3.29201, 3.2919], [10.8101, 10.8059, 10.7828, 10.7433, 10.7328, 10.7288, 10.7223, 10.719, 10.7137, 8.06407, 6.97895, 5.96351, 5.29587, 4.61244, 4.24989, 4.15048, 4.10516, 3.97209, 3.73112, 3.65571, 3.59613, 3.54736, 3.49782, 3.46525, 3.45247, 3.42823, 3.41281, 3.40374, 3.39741, 3.39535, 3.39252, 3.39032, 3.38787, 3.38416, 3.38246, 3.38174, 3.38106, 3.38023, 3.37951, 3.37901, 3.37835, 3.37745, 3.37694, 3.37627, 3.37598, 3.37572, 3.3754, 3.37526, 3.3752, 3.37514, 3.37506, 3.37502, 3.37499, 3.37498, 3.37497, 3.37496, 3.37495, 3.37494, 3.37494, 3.37494, 3.37494, 3.37494, 3.37493, 3.37493, 3.37493]}} ...
);

% Tuning error = 1.361105
% i_pass = 4, i_var = 6, i_mult = 2
% Hyperparams written at 2017-06-05, 07:22:13:132
