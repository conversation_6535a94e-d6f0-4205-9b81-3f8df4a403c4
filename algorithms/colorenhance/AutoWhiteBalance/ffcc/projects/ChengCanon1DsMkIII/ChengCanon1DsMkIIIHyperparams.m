% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengCanon1DsMkIIIHyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:23:080.

params.HYPERPARAMS = struct( ...
'VONMISES_MULTIPLIER', 2^-9, ...
'CROSSENT_MULTIPLIER', 2^-1.75, ...
'FILTER_SHIFTS', [2^-71.5, 2^-36.25], ...
'FILTER_MULTIPLIERS', [2^-28.25, 2^-41.5], ...
'BIAS_SHIFT', 2^-36.5, ...
'BIAS_MULTIPLIER', 2^-28, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.26339, ...
    'mean2', 3.35072, ...
    'mean4', 5.63526, ...
    'median', 1.39682, ...
    'tri', 1.59802, ...
    'b25', 0.391184, ...
    'w25', 5.53866, ...
    'w05', 10.1855, ...
    'max', 16.5537 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 3.05511, ...
    'mean2', 4.49848, ...
    'mean4', 7.54326, ...
    'median', 1.89502, ...
    'tri', 2.13382, ...
    'b25', 0.561959, ...
    'w25', 7.45216, ...
    'w05', 13.6979, ...
    'max', 22.7906 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 2.75941, ...
    'mean2', 5.6862, ...
    'mean4', 18.0598, ...
    'median', 1.92094, ...
    'tri', 2.05211, ...
    'b25', 0.921985, ...
    'w25', 6.11993, ...
    'w05', 14.965, ...
    'max', 71.8885 ...
)    , ...
'uv_bin_bias', [0.0698825, -0.194411], ...
'final_losses', [0.900958, 0.841842, 0.831957], ...
'train_times', [453.142, 272.728, 282.218], ...
'min_feature_time', 0.048847, ...
'min_filter_time', 0.000939, ...
'median_feature_time', 0.326612, ...
'median_filter_time', 0.005093, ...
'opt_traces', ...
  {{[437.701, 243.392, 217.872, 208.512, 197.946, 187.162, 182.832, 173.925, 166.128, 160.202, 153.507, 149.409, 146.886, 141.879, 140.598, 136.954, 135.555], [422.864, 222.446, 202.68, 194.494, 182.523, 173.997, 163.763, 151.987, 143.745, 136.579, 130.637, 127.249, 123.452, 120.5, 119.088, 116.03, 113.876], [420.391, 231.826, 209.837, 199.972, 187.42, 176.984, 171.025, 155.163, 148.419, 139.384, 131.681, 127.211, 125.325, 122.507, 119.811, 117.031, 115.482]; ...
    [2.79298, 2.48854, 2.06294, 1.46394, 1.2765, 1.08093, 1.02364, 0.98715, 0.944832, 0.931516, 0.921702, 0.915044, 0.910867, 0.908193, 0.906046, 0.904565, 0.903849, 0.903091, 0.902727, 0.902277, 0.902057, 0.902021, 0.901924, 0.901817, 0.901721, 0.901659, 0.901552, 0.90152, 0.901495, 0.901478, 0.901465, 0.901459, 0.901451, 0.901441, 0.901435, 0.901427, 0.901422, 0.901416, 0.901411, 0.901409, 0.901377, 0.90135, 0.901328, 0.901322, 0.901322, 0.901286, 0.901257, 0.901228, 0.901197, 0.901161, 0.901104, 0.901051, 0.901019, 0.900977, 0.900963, 0.900958], [3.21919, 2.8521, 1.77681, 1.24535, 1.08078, 0.997536, 0.939192, 0.892748, 0.882652, 0.870914, 0.863687, 0.857635, 0.851556, 0.850622, 0.847528, 0.847179, 0.846801, 0.845688, 0.845052, 0.844543, 0.84428, 0.844135, 0.84391, 0.843791, 0.843656, 0.843522, 0.843419, 0.843219, 0.843108, 0.843032, 0.842904, 0.84276, 0.84243, 0.842237, 0.842113, 0.841988, 0.841885, 0.841881, 0.84187, 0.841859, 0.841853, 0.841848, 0.841845, 0.841843, 0.841843, 0.841843, 0.841843, 0.841843, 0.841843, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842, 0.841842], [3.35952, 2.96309, 1.92331, 1.54818, 1.32427, 1.04523, 1.0059, 0.965931, 0.910541, 0.886533, 0.874462, 0.862297, 0.85736, 0.854366, 0.852378, 0.851372, 0.85059, 0.849966, 0.849671, 0.849442, 0.8493, 0.849183, 0.848942, 0.848724, 0.848401, 0.848171, 0.847792, 0.847353, 0.846791, 0.846308, 0.844607, 0.842694, 0.841539, 0.840396, 0.839856, 0.839194, 0.838853, 0.83858, 0.838238, 0.838132, 0.838045, 0.83789, 0.837764, 0.83753, 0.837105, 0.836639, 0.836339, 0.835799, 0.835362, 0.835105, 0.834764, 0.834176, 0.83357, 0.833136, 0.832855, 0.832673, 0.832521, 0.832424, 0.832287, 0.83224, 0.832197, 0.832123, 0.832025, 0.832002, 0.831957]}} ...
);

% Tuning error = 1.613816
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-06-04, 21:38:55:149
