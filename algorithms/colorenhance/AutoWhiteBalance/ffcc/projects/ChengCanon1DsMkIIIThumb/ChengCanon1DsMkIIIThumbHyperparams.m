% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengCanon1DsMkIIIThumbHyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:23:158.

params.HYPERPARAMS = struct( ...
'CROSSENT_MULTIPLIER', 2^-5.25, ...
'VONMISES_MULTIPLIER', 2^2.5, ...
'FILTER_MULTIPLIERS', [2^-23.5, 2^-22.5], ...
'BIAS_MULTIPLIER', 2^-16, ...
'FILTER_SHIFTS', [2^-40.25, 2^-38], ...
'BIAS_SHIFT', 2^-37.75, ...
'VON_MISES_DIAGONAL_EPS', 2^0.5 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.23476, ...
    'mean2', 3.23537, ...
    'mean4', 5.23221, ...
    'median', 1.34146, ...
    'tri', 1.55982, ...
    'b25', 0.459111, ...
    'w25', 5.37594, ...
    'w05', 10.0058, ...
    'max', 15.1816 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 3.05216, ...
    'mean2', 4.45587, ...
    'mean4', 7.38195, ...
    'median', 1.86165, ...
    'tri', 2.13291, ...
    'b25', 0.667913, ...
    'w25', 7.32376, ...
    'w05', 14.0256, ...
    'max', 21.3534 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 2.51061, ...
    'mean2', 6.31311, ...
    'mean4', 21.7523, ...
    'median', 1.52466, ...
    'tri', 1.6526, ...
    'b25', 0.543421, ...
    'w25', 6.3237, ...
    'w05', 15.6868, ...
    'max', 87.0819 ...
)    , ...
'uv_bin_bias', [0.0837935, -0.0183934], ...
'final_losses', [1688.59, 1590.86, 1593.91], ...
'train_times', [185.795, 200.638, 150.149], ...
'min_feature_time', 0.001827, ...
'min_filter_time', 0.000839, ...
'median_feature_time', 0.012096, ...
'median_filter_time', 0.006265, ...
'opt_traces', ...
  {{[38.6877, 26.7879, 25.8619, 24.8999, 24.6522, 24.5729, 24.5311, 24.5111, 24.5072, 24.5062, 24.5061, 24.5061, 24.506, 24.506, 24.506, 24.506, 24.506], [37.3762, 25.9904, 24.9383, 23.8998, 23.6087, 23.5312, 23.4814, 23.455, 23.4504, 23.4497, 23.4493, 23.4493, 23.4493, 23.4493, 23.4493, 23.4493, 23.4493], [37.1576, 25.7286, 24.7905, 23.686, 23.4529, 23.3359, 23.2891, 23.2681, 23.2614, 23.2591, 23.2591, 23.259, 23.259, 23.2589, 23.2589, 23.2589, 23.2589]; ...
    [3486.41, 3406.27, 3087.16, 2973.22, 2887.26, 2778.69, 2662.55, 2606.11, 2372.04, 2316.67, 2285.12, 2255.19, 2223.57, 2154.11, 2117.82, 2087.52, 2045.51, 2016.19, 1992.81, 1966.05, 1927.41, 1897.6, 1867.07, 1853.61, 1841.8, 1830.68, 1814.1, 1797.36, 1788.24, 1778.82, 1766.38, 1760.52, 1756.7, 1752.03, 1745.33, 1741.25, 1738.33, 1735.53, 1732.61, 1728.03, 1725.05, 1720.15, 1717.01, 1713.94, 1711.91, 1709.71, 1707.55, 1706.05, 1704.74, 1703.47, 1701.72, 1699.19, 1698.07, 1697.5, 1696.94, 1695.99, 1694.71, 1693.89, 1693.36, 1692.98, 1691.83, 1690.95, 1690.4, 1689.71, 1688.59], [3382.98, 3239.94, 2976.86, 2861.65, 2781.74, 2580.74, 2421.45, 2300.37, 2205.98, 2124.42, 2087.15, 2054.28, 1975.35, 1954.8, 1933.59, 1889.38, 1835.67, 1803.59, 1787.67, 1768.33, 1759.13, 1740.84, 1721.46, 1703.4, 1692.19, 1685.83, 1678.81, 1670.12, 1663.83, 1656, 1648.27, 1642.76, 1636.36, 1632.4, 1628.29, 1626.7, 1624.56, 1621.54, 1620.2, 1617.96, 1616.03, 1614.26, 1611.71, 1609.92, 1608.26, 1607.05, 1606.58, 1605.75, 1604.58, 1602.81, 1601.67, 1600.41, 1599.78, 1598.94, 1598.23, 1596.72, 1595.9, 1595.14, 1594.15, 1593.67, 1593, 1592.47, 1591.84, 1591.2, 1590.86], [3384.77, 3088.26, 3010.13, 2891.81, 2758.51, 2492.76, 2442.98, 2354.66, 2319.46, 2230.05, 2194.71, 2099.59, 2078.54, 2055.02, 2035.69, 2021.81, 1994.12, 1940.56, 1905.5, 1879.29, 1854.34, 1833.55, 1809.16, 1785.3, 1759.31, 1746.83, 1742.2, 1731.9, 1722.7, 1705.78, 1695.18, 1688.27, 1684.24, 1676.61, 1669.78, 1664.45, 1661.86, 1660.46, 1659.18, 1657.58, 1653.25, 1649.31, 1643.28, 1640.44, 1636.48, 1633.01, 1629.47, 1626.24, 1624.69, 1623.17, 1620.94, 1618.58, 1615.93, 1613.59, 1611.93, 1610.45, 1608.57, 1605.99, 1604.15, 1602.61, 1600.27, 1598.26, 1596.68, 1594.78, 1593.91]}} ...
);

% Tuning error = 1.630989
% i_pass = 3, i_var = 6, i_mult = 2
% Hyperparams written at 2017-06-05, 05:21:26:474
