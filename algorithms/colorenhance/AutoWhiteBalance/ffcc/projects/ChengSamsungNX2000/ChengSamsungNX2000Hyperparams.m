% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengSamsungNX2000Hyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:23:504.

params.HYPERPARAMS = struct( ...
'VONMISES_MULTIPLIER', 2^2, ...
'CROSSENT_MULTIPLIER', 2^-18.5, ...
'FILTER_SHIFTS', [2^-40, 2^-31], ...
'FILTER_MULTIPLIERS', [2^-23.5, 2^-34.25], ...
'BIAS_SHIFT', 2^-39, ...
'BIAS_MULTIPLIER', 2^-19, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.30565, ...
    'mean2', 3.49282, ...
    'mean4', 5.69064, ...
    'median', 1.27752, ...
    'tri', 1.48361, ...
    'b25', 0.416484, ...
    'w25', 5.86803, ...
    'w05', 10.9726, ...
    'max', 15.0263 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 3.2884, ...
    'mean2', 4.97029, ...
    'mean4', 8.04508, ...
    'median', 1.92062, ...
    'tri', 2.12293, ...
    'b25', 0.63087, ...
    'w25', 8.36066, ...
    'w05', 15.5192, ...
    'max', 20.8744 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 3.7731, ...
    'mean2', 9.1563, ...
    'mean4', 23.3681, ...
    'median', 1.72664, ...
    'tri', 1.94389, ...
    'b25', 0.642194, ...
    'w25', 10.6558, ...
    'w05', 31.7263, ...
    'max', 77.7602 ...
)    , ...
'uv_bin_bias', [0.212457, -0.0797438], ...
'final_losses', [868.169, 840.612, 845.155], ...
'train_times', [194.857, 239.511, 185.679], ...
'min_feature_time', 0.052767, ...
'min_filter_time', 0.000939, ...
'median_feature_time', 0.313659, ...
'median_filter_time', 0.001132, ...
'opt_traces', ...
  {{[0.00309621, 0.00309441, 0.00309441, 0.00309441, 0.00309441, 0.00309441, 0.00309441, 0.00309441, 0.00309441], [0.00300647, 0.00300467, 0.00300467, 0.00300467], [0.0029616, 0.00295993, 0.00295993, 0.00295993, 0.00295993]; ...
    [3263.08, 3261.05, 3251.78, 3249.82, 3247.59, 3247.29, 3247.08, 3246.1, 3245.37, 3243.16, 3241.83, 3241.25, 3240.53, 3239.86, 3238.67, 2350.5, 1906.21, 1681.96, 1531.83, 1391.35, 1316.98, 1268.26, 1245.54, 1215.17, 1181.87, 1130.16, 1098.91, 1074.33, 1054.23, 1038.09, 999.75, 982.472, 973.368, 966.591, 952.02, 940.909, 933.2, 929.678, 925.945, 921.219, 914.155, 908.263, 903.597, 899.396, 896.796, 892.96, 890.412, 887.574, 885.339, 883.357, 882.425, 881.263, 880.062, 878.606, 876.991, 875.733, 875.012, 874.357, 873.513, 872.285, 871.175, 870.189, 869.472, 869.018, 868.169], [3154.21, 3152.96, 3151.15, 3149.61, 3146.64, 3146.46, 3146.24, 3146.05, 3145.92, 3145.79, 3145.68, 3145.48, 2405.17, 2028.04, 1777.69, 1574.32, 1471.85, 1381.43, 1335.05, 1299.9, 1231.65, 1183.55, 1127.86, 1095.56, 1060.51, 1038.48, 1026.51, 1010.43, 990.722, 976.723, 965.076, 945.919, 937.588, 917.963, 912.684, 909.596, 904.52, 895.484, 890.737, 886.362, 881.516, 878.825, 873.081, 868.862, 865.949, 863.14, 861.02, 858.69, 856.667, 855.546, 854.726, 852.875, 851.392, 849.753, 847.799, 846.108, 845.064, 844.172, 843.343, 842.595, 841.986, 841.437, 841.098, 840.927, 840.612], [3122.73, 3121.17, 3111.13, 3109.75, 3108, 3107.67, 3107.3, 3106.01, 3103.91, 3101.75, 3100.1, 3099.35, 3099.07, 3098.86, 3098.55, 3098.09, 3097.47, 3096.35, 2341.15, 1881.48, 1656.89, 1526.47, 1358.85, 1305.56, 1234.57, 1223.97, 1204.84, 1184.6, 1145.28, 1115.61, 1097.09, 1071.2, 1039.23, 1007.35, 992.733, 986.6, 977.085, 955.545, 941.66, 931.845, 928.015, 918.875, 903.713, 897.773, 891.86, 886.907, 883.506, 877.49, 872.254, 868.844, 866.076, 863.299, 860.754, 858.876, 857.632, 856.258, 854.094, 852.314, 850.908, 849.752, 848.327, 847.14, 846.524, 845.896, 845.155]}} ...
);

% Tuning error = 1.605883
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-06-04, 21:32:15:193
