% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengCanon600DThumbHyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:22:797.

params.HYPERPARAMS = struct( ...
'CROSSENT_MULTIPLIER', 2^-25.25, ...
'VONMISES_MULTIPLIER', 2^-1.5, ...
'FILTER_MULTIPLIERS', [2^-27.5, 2^-24.5], ...
'BIAS_MULTIPLIER', 2^-21.75, ...
'FILTER_SHIFTS', [2^-39.25, 2^-38.75], ...
'BIAS_SHIFT', 2^-44.75, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.21308, ...
    'mean2', 3.31039, ...
    'mean4', 5.45307, ...
    'median', 1.32629, ...
    'tri', 1.55155, ...
    'b25', 0.360832, ...
    'w25', 5.56473, ...
    'w05', 10.291, ...
    'max', 15.1141 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 2.98046, ...
    'mean2', 4.46542, ...
    'mean4', 7.4434, ...
    'median', 1.73037, ...
    'tri', 2.0664, ...
    'b25', 0.537558, ...
    'w25', 7.45948, ...
    'w05', 13.9677, ...
    'max', 20.5523 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 3.10373, ...
    'mean2', 6.25656, ...
    'mean4', 14.0134, ...
    'median', 1.59314, ...
    'tri', 1.7408, ...
    'b25', 0.687272, ...
    'w25', 8.34861, ...
    'w05', 22.9902, ...
    'max', 45.2841 ...
)    , ...
'uv_bin_bias', [-0.0104565, 0.202082], ...
'final_losses', [89.4994, 90.1297, 72.5432], ...
'train_times', [204.28, 230.531, 227.145], ...
'min_feature_time', 0.001955, ...
'min_filter_time', 0.000905, ...
'median_feature_time', 0.0165585, ...
'median_filter_time', 0.001857, ...
'opt_traces', ...
  {{[2.8349e-05, 2.83473e-05, 2.83473e-05, 2.83473e-05, 2.83473e-05, 2.83473e-05, 2.83473e-05], [2.77237e-05, 2.7722e-05, 2.7722e-05, 2.7722e-05, 2.7722e-05], [2.73068e-05, 2.7305e-05, 2.7305e-05, 2.7305e-05, 2.7305e-05, 2.7305e-05, 2.7305e-05]; ...
    [284.644, 284.574, 284.507, 284.186, 283.189, 283.123, 282.935, 282.881, 282.858, 282.795, 282.692, 282.57, 282.445, 282.352, 282.322, 282.275, 282.238, 282.192, 282.173, 282.17, 282.165, 282.156, 282.149, 282.133, 282.112, 282.108, 282.1, 282.098, 282.096, 282.096, 282.096, 282.095, 282.095, 282.095, 282.095, 282.094, 282.092, 282.09, 282.089, 282.088, 282.088, 282.088, 282.087, 282.084, 204.341, 172.921, 152.555, 140.518, 125.304, 121.993, 120.558, 118.251, 114.138, 111.354, 107.918, 106.082, 101.394, 98.6557, 96.7912, 95.5425, 94.4134, 92.8071, 91.459, 90.2276, 89.4994], [278.212, 278.179, 277.948, 277.694, 277.523, 277.361, 277.09, 277.028, 276.949, 276.913, 276.891, 276.849, 276.818, 276.783, 276.677, 276.519, 276.397, 276.254, 276.163, 276.12, 276.037, 275.991, 275.97, 275.96, 275.949, 275.94, 275.928, 275.91, 275.893, 275.877, 275.856, 275.851, 275.846, 275.842, 275.838, 275.837, 275.836, 275.836, 275.836, 275.836, 275.836, 275.836, 275.835, 275.834, 196.558, 194.424, 181.432, 148.454, 136.3, 128.316, 119.547, 114.827, 111.466, 108.429, 106.008, 103.455, 101.15, 97.5313, 95.9748, 94.9491, 94.3694, 93.7287, 92.8972, 91.4682, 90.1297], [272.525, 272.399, 272.227, 271.964, 271.794, 271.765, 271.759, 271.721, 271.703, 271.697, 271.695, 271.692, 271.69, 271.687, 271.684, 271.678, 271.677, 271.676, 271.676, 271.675, 271.675, 271.674, 271.674, 271.673, 271.673, 271.672, 271.672, 271.671, 182.555, 169.497, 147.719, 130.249, 124.273, 118.291, 116.447, 112.031, 108.633, 102.391, 98.1186, 95.3352, 93.9854, 91.8995, 90.1253, 88.3582, 86.0428, 84.3133, 82.1658, 81.0908, 80.2744, 79.3202, 78.6935, 77.747, 76.9377, 76.2048, 75.4188, 75.1131, 74.8451, 74.4453, 73.9696, 73.694, 73.492, 73.1122, 72.8144, 72.6617, 72.5432]}} ...
);

% Tuning error = 1.556791
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-06-04, 21:32:41:734
