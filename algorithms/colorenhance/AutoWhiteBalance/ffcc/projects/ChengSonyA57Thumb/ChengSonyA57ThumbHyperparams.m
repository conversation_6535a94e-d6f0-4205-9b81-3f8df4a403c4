% Copyright 2017 Google Inc.
%
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
%
%      http://www.apache.org/licenses/LICENSE-2.0
%
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengSonyA57ThumbHyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:22:282.

params.HYPERPARAMS = struct( ...
'CROSSENT_MULTIPLIER', 2^-13, ...
'VONMISES_MULTIPLIER', 2^-3, ...
'FILTER_MULTIPLIERS', [2^-27.75, 2^-28], ...
'BIAS_MULTIPLIER', 2^-23.5, ...
'FILTER_SHIFTS', [2^-41.75, 2^-42.25], ...
'BIAS_SHIFT', 2^-39.75, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.10751, ...
    'mean2', 3.01617, ...
    'mean4', 4.96383, ...
    'median', 1.35015, ...
    'tri', 1.53108, ...
    'b25', 0.41456, ...
    'w25', 4.93881, ...
    'w05', 9.2399, ...
    'max', 14.6984 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 3.10158, ...
    'mean2', 4.42853, ...
    'mean4', 7.26692, ...
    'median', 2.09689, ...
    'tri', 2.29645, ...
    'b25', 0.662893, ...
    'w25', 7.31803, ...
    'w05', 13.5082, ...
    'max', 21.0612 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 3.61296, ...
    'mean2', 10.9237, ...
    'mean4', 34.4526, ...
    'median', 1.82751, ...
    'tri', 1.98293, ...
    'b25', 0.74986, ...
    'w25', 9.85661, ...
    'w05', 30.8186, ...
    'max', 124.415 ...
)    , ...
'uv_bin_bias', [0.223554, -0.203551], ...
'final_losses', [44.7101, 43.2115, 43.8352], ...
'train_times', [238.062, 270.299, 269.707], ...
'min_feature_time', 0.001777, ...
'min_filter_time', 0.00096, ...
'median_feature_time', 0.009585, ...
'median_filter_time', 0.0056145, ...
'opt_traces', ...
  {{[0.184794, 0.150434, 0.148005, 0.147275, 0.147261, 0.147261, 0.14726, 0.14726, 0.14726, 0.14726, 0.14726, 0.14726, 0.14726, 0.14726, 0.14726, 0.14726, 0.14726], [0.179717, 0.148037, 0.145642, 0.144988, 0.144969, 0.144968, 0.144968, 0.144968, 0.144968, 0.144968, 0.144968, 0.144968, 0.144968, 0.144968, 0.144968, 0.144968, 0.144968], [0.179717, 0.147194, 0.144851, 0.144153, 0.144139, 0.144138, 0.144138, 0.144138, 0.144138, 0.144138, 0.144138, 0.144138, 0.144138, 0.144138, 0.144138, 0.144138, 0.144138]; ...
    [113.379, 100.939, 87.9209, 73.9139, 68.848, 66.0262, 63.1035, 62.1555, 60.6035, 58.1044, 56.4089, 54.7315, 53.4949, 52.3563, 51.2471, 50.3049, 50.0307, 49.7277, 49.312, 48.7685, 48.1698, 47.93, 47.7581, 47.4163, 47.1081, 46.8708, 46.6632, 46.4658, 46.302, 46.2244, 46.1178, 45.978, 45.8704, 45.7954, 45.7113, 45.6259, 45.5374, 45.4611, 45.3741, 45.3131, 45.2694, 45.2375, 45.2198, 45.2012, 45.1723, 45.1445, 45.1172, 45.0959, 45.0736, 45.0457, 45.0146, 44.9917, 44.9584, 44.9305, 44.882, 44.848, 44.826, 44.8029, 44.7905, 44.7784, 44.7679, 44.7525, 44.7342, 44.7215, 44.7101], [110.786, 98.1613, 80.1844, 71.2776, 67.2512, 63.9268, 62.843, 59.3182, 57.1161, 55.3424, 52.9631, 51.79, 51.0147, 50.0977, 49.4238, 48.9935, 48.418, 47.9682, 47.2601, 46.8749, 46.6111, 46.4625, 46.1717, 45.7182, 45.4395, 45.2157, 45.1034, 44.9729, 44.7442, 44.6232, 44.5389, 44.4772, 44.444, 44.3668, 44.2781, 44.1974, 44.1415, 44.0963, 44.0469, 43.9398, 43.8836, 43.8324, 43.7811, 43.6942, 43.6425, 43.5658, 43.5256, 43.4974, 43.4708, 43.4323, 43.3934, 43.3686, 43.3504, 43.3428, 43.3329, 43.3209, 43.311, 43.3008, 43.2917, 43.2788, 43.2653, 43.2491, 43.2321, 43.2186, 43.2115], [110.557, 98.0128, 83.3314, 74.0346, 69.9974, 66.7953, 64.9567, 61.0678, 58.0248, 57.2055, 56.4296, 54.7677, 53.9078, 52.6189, 51.9991, 50.3378, 49.7376, 49.1257, 48.6288, 48.174, 47.5733, 47.1761, 46.8013, 46.5929, 46.482, 46.3589, 46.2043, 46.0814, 45.8607, 45.6997, 45.5739, 45.43, 45.2835, 45.2048, 45.1395, 45.0738, 44.9701, 44.8642, 44.7845, 44.7372, 44.7042, 44.6608, 44.6071, 44.5549, 44.5121, 44.4695, 44.4247, 44.328, 44.2596, 44.2083, 44.1636, 44.1225, 44.0799, 44.0431, 44.0076, 43.9726, 43.9319, 43.9107, 43.8949, 43.8842, 43.8756, 43.8652, 43.8502, 43.8418, 43.8352]}} ...
);

% Tuning error = 1.549075
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-06-04, 21:34:44:165
