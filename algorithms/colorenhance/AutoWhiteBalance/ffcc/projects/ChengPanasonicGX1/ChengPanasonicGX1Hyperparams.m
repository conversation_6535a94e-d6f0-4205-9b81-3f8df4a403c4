% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengPanasonicGX1Hyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:23:433.

params.HYPERPARAMS = struct( ...
'VONMISES_MULTIPLIER', 2^-5, ...
'CROSSENT_MULTIPLIER', 2^-9.5, ...
'FILTER_SHIFTS', [2^-55, 2^-34], ...
'FILTER_MULTIPLIERS', [2^-23.75, 2^-30], ...
'BIAS_SHIFT', 2^-34.75, ...
'BIAS_MULTIPLIER', 2^-29, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.08229, ...
    'mean2', 3.06819, ...
    'mean4', 5.13364, ...
    'median', 1.29913, ...
    'tri', 1.46004, ...
    'b25', 0.431739, ...
    'w25', 4.98215, ...
    'w05', 9.76257, ...
    'max', 14.3761 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 2.91685, ...
    'mean2', 4.19427, ...
    'mean4', 6.8552, ...
    'median', 2.01485, ...
    'tri', 2.14099, ...
    'b25', 0.653316, ...
    'w25', 6.84082, ...
    'w05', 13.0614, ...
    'max', 19.4001 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 2.67444, ...
    'mean2', 4.20831, ...
    'mean4', 9.14278, ...
    'median', 1.97055, ...
    'tri', 1.99663, ...
    'b25', 0.817132, ...
    'w25', 5.95446, ...
    'w05', 13.5614, ...
    'max', 30.1115 ...
)    , ...
'uv_bin_bias', [0.0285835, 0.0161794], ...
'final_losses', [8.33372, 9.13116, 9.01144], ...
'train_times', [194.523, 174.845, 195.165], ...
'min_feature_time', 0.057022, ...
'min_filter_time', 0.000954, ...
'median_feature_time', 0.311746, ...
'median_filter_time', 0.001116, ...
'opt_traces', ...
  {{[1.58526, 1.41786, 0.973288, 0.881458, 0.8481, 0.797545, 0.78423, 0.778172, 0.77206, 0.768374, 0.764555, 0.764021, 0.762655, 0.762413, 0.762229, 0.762101, 0.762065], [1.5508, 1.5389, 1.04632, 0.92039, 0.882341, 0.817784, 0.794301, 0.788178, 0.78479, 0.779109, 0.776375, 0.775726, 0.775484, 0.77539, 0.775321, 0.77527, 0.775258], [1.52782, 1.42593, 0.972519, 0.887458, 0.857822, 0.789817, 0.774704, 0.7664, 0.759644, 0.757434, 0.755206, 0.75392, 0.753633, 0.753421, 0.75338, 0.753334, 0.753309]; ...
    [15.9571, 12.9599, 12.3091, 11.2684, 10.3984, 10.2437, 10.1018, 10.0019, 9.7148, 9.51441, 9.34139, 9.24617, 9.17306, 9.1082, 8.93643, 8.84072, 8.78727, 8.76642, 8.73626, 8.69259, 8.6525, 8.62935, 8.60865, 8.59278, 8.57823, 8.56202, 8.54343, 8.52138, 8.49871, 8.48024, 8.46817, 8.45956, 8.44517, 8.43585, 8.43021, 8.42799, 8.42552, 8.42044, 8.41405, 8.4066, 8.3996, 8.39594, 8.39274, 8.38859, 8.38569, 8.3823, 8.38076, 8.37935, 8.37723, 8.37496, 8.373, 8.3705, 8.36636, 8.36172, 8.35872, 8.35598, 8.35309, 8.3507, 8.34756, 8.34485, 8.3417, 8.33881, 8.33649, 8.33536, 8.33372], [16.2165, 13.5478, 13.0257, 11.8585, 11.5789, 11.5127, 11.2968, 11.0723, 10.8488, 10.7365, 10.646, 10.4453, 10.3718, 10.3086, 10.2382, 10.2118, 10.1599, 10.0814, 9.93461, 9.83809, 9.78757, 9.76097, 9.7122, 9.6818, 9.65143, 9.62252, 9.567, 9.52797, 9.49153, 9.46742, 9.44827, 9.42048, 9.40754, 9.39932, 9.39121, 9.37489, 9.35815, 9.33544, 9.31693, 9.31172, 9.30777, 9.29809, 9.27772, 9.25569, 9.24463, 9.24025, 9.23697, 9.23305, 9.22548, 9.20841, 9.19431, 9.18574, 9.18212, 9.17944, 9.17216, 9.16702, 9.16211, 9.15778, 9.15413, 9.15138, 9.14903, 9.14659, 9.13919, 9.13531, 9.13116], [15.8596, 13.2853, 12.6239, 12.2718, 11.2116, 11.1014, 11.0011, 10.8652, 10.4964, 10.2803, 10.1834, 10.1375, 10.0746, 9.95021, 9.84384, 9.76157, 9.70131, 9.64515, 9.60382, 9.55506, 9.52747, 9.49135, 9.45577, 9.40083, 9.36887, 9.3289, 9.30284, 9.28289, 9.26394, 9.23793, 9.21473, 9.18782, 9.17546, 9.1636, 9.15559, 9.14134, 9.13097, 9.11632, 9.10799, 9.09847, 9.09567, 9.09093, 9.08366, 9.07566, 9.06899, 9.06202, 9.05673, 9.05206, 9.0487, 9.04475, 9.04178, 9.03998, 9.03831, 9.03606, 9.03296, 9.03035, 9.02645, 9.02364, 9.0218, 9.02065, 9.01952, 9.01728, 9.01509, 9.01312, 9.01144]}} ...
);

% Tuning error = 1.534048
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-06-04, 21:31:15:523
