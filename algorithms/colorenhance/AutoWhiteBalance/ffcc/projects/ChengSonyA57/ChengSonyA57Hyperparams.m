% Copyright 2017 Google Inc.
%
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
%
%      http://www.apache.org/licenses/LICENSE-2.0
%
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengSonyA57Hyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:00:947.

params.HYPERPARAMS = struct( ...
'VONMISES_MULTIPLIER', 2^-7, ...
'CROSSENT_MULTIPLIER', 2^-16.25, ...
'FILTER_SHIFTS', [2^-43.75, 2^-39], ...
'FILTER_MULTIPLIERS', [2^-22.5, 2^-35], ...
'BIAS_SHIFT', 2^-39.75, ...
'BIAS_MULTIPLIER', 2^-31, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.02852, ...
    'mean2', 2.90667, ...
    'mean4', 4.7738, ...
    'median', 1.3683, ...
    'tri', 1.47536, ...
    'b25', 0.380069, ...
    'w25', 4.78411, ...
    'w05', 8.81619, ...
    'max', 15.1326 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 2.93539, ...
    'mean2', 4.16662, ...
    'mean4', 6.8219, ...
    'median', 2.0025, ...
    'tri', 2.18251, ...
    'b25', 0.596768, ...
    'w25', 6.84405, ...
    'w05', 12.5044, ...
    'max', 21.6374 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 2.93046, ...
    'mean2', 5.76573, ...
    'mean4', 15.8454, ...
    'median', 1.68481, ...
    'tri', 1.82818, ...
    'b25', 0.778956, ...
    'w25', 7.38155, ...
    'w05', 18.8755, ...
    'max', 62.7749 ...
)    , ...
'uv_bin_bias', [0.214045, -0.240159], ...
'final_losses', [2.65966, 2.66818, 2.68756], ...
'train_times', [222.684, 263.613, 277.088], ...
'min_feature_time', 0.034196, ...
'min_filter_time', 0.00083, ...
'median_feature_time', 0.063737, ...
'median_filter_time', 0.00203, ...
'opt_traces', ...
  {{[0.0194241, 0.0137997, 0.0134106, 0.0133449, 0.0133257, 0.0133237, 0.0133237, 0.0133236, 0.0133236, 0.0133236, 0.0133236, 0.0133236, 0.0133236, 0.0133236, 0.0133236, 0.0133236, 0.0133236], [0.0188905, 0.0137551, 0.0134457, 0.0133888, 0.0133728, 0.0133715, 0.0133714, 0.0133714, 0.0133714, 0.0133714, 0.0133714, 0.0133714, 0.0133714, 0.0133714, 0.0133714, 0.0133714, 0.0133714], [0.0188905, 0.0135296, 0.0132046, 0.0131455, 0.0131292, 0.013128, 0.0131279, 0.0131279, 0.0131279, 0.0131279, 0.0131279, 0.0131279, 0.0131279, 0.0131279, 0.0131279, 0.0131279, 0.0131279]; ...
    [7.19654, 4.90319, 4.4885, 4.16655, 3.76053, 3.66586, 3.57644, 3.55859, 3.48495, 3.43442, 3.32096, 3.2323, 3.17287, 3.12149, 3.08277, 3.07321, 3.02455, 2.99958, 2.97966, 2.96054, 2.9384, 2.92541, 2.89981, 2.88026, 2.86799, 2.85121, 2.84049, 2.83208, 2.82273, 2.81605, 2.80795, 2.79773, 2.78583, 2.77199, 2.76744, 2.76, 2.75719, 2.75318, 2.74722, 2.74041, 2.73488, 2.73274, 2.73032, 2.72757, 2.72302, 2.71786, 2.71403, 2.71031, 2.70824, 2.70655, 2.70316, 2.69837, 2.69405, 2.68947, 2.68646, 2.68218, 2.68102, 2.67869, 2.67341, 2.66972, 2.66663, 2.66468, 2.66265, 2.66057, 2.65966], [7.08945, 4.88859, 4.49622, 4.00173, 3.69425, 3.5785, 3.45356, 3.35233, 3.22698, 3.16687, 3.15128, 3.13667, 3.08523, 3.0468, 3.00166, 2.97154, 2.94863, 2.93606, 2.92629, 2.90393, 2.88486, 2.8701, 2.85327, 2.84266, 2.82511, 2.81173, 2.79609, 2.78339, 2.77473, 2.76842, 2.7642, 2.75761, 2.75257, 2.74519, 2.7392, 2.73329, 2.72838, 2.7236, 2.71942, 2.71685, 2.71312, 2.70955, 2.70677, 2.70477, 2.70276, 2.70135, 2.6988, 2.69575, 2.69268, 2.68955, 2.68727, 2.68604, 2.68479, 2.68291, 2.6816, 2.6795, 2.67779, 2.67513, 2.67335, 2.67182, 2.67077, 2.67, 2.66948, 2.66884, 2.66818], [7.05575, 4.873, 4.30906, 3.79566, 3.66987, 3.5266, 3.47692, 3.44295, 3.39635, 3.34523, 3.24885, 3.21705, 3.20291, 3.1315, 3.07682, 3.02674, 3.00206, 2.99108, 2.97707, 2.9475, 2.92966, 2.91042, 2.88525, 2.86967, 2.86182, 2.85333, 2.83914, 2.82704, 2.81852, 2.81271, 2.80534, 2.79957, 2.78465, 2.77735, 2.77, 2.76713, 2.76472, 2.76063, 2.75697, 2.75318, 2.74947, 2.74526, 2.74093, 2.73755, 2.73341, 2.73084, 2.72874, 2.72758, 2.72544, 2.72118, 2.71865, 2.71563, 2.7106, 2.70809, 2.70551, 2.70268, 2.70124, 2.69984, 2.69866, 2.69753, 2.69434, 2.69242, 2.69048, 2.68878, 2.68756]}} ...
);

% Tuning error = 1.494117
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-06-04, 21:34:08:901
