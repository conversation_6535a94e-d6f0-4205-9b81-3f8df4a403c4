% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengCanon600DHyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:22:838.

params.HYPERPARAMS = struct( ...
'VONMISES_MULTIPLIER', 2^-4, ...
'CROSSENT_MULTIPLIER', 2^-25.5, ...
'FILTER_SHIFTS', [2^-68.25, 2^-40.5], ...
'FILTER_MULTIPLIERS', [2^-21.5, 2^-29], ...
'BIAS_SHIFT', 2^-67, ...
'BIAS_MULTIPLIER', 2^-23, ...
'VON_MISES_DIAGONAL_EPS', 2^-0.25 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.14321, ...
    'mean2', 3.19126, ...
    'mean4', 5.26161, ...
    'median', 1.31957, ...
    'tri', 1.51501, ...
    'b25', 0.363035, ...
    'w25', 5.26402, ...
    'w05', 10.1254, ...
    'max', 14.1859 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 2.90102, ...
    'mean2', 4.34934, ...
    'mean4', 7.22353, ...
    'median', 1.76473, ...
    'tri', 2.05435, ...
    'b25', 0.515352, ...
    'w25', 7.17295, ...
    'w05', 13.681, ...
    'max', 19.5182 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 2.72412, ...
    'mean2', 4.46362, ...
    'mean4', 9.5529, ...
    'median', 1.79454, ...
    'tri', 1.91799, ...
    'b25', 0.873938, ...
    'w25', 6.25264, ...
    'w05', 14.7385, ...
    'max', 27.73 ...
)    , ...
'uv_bin_bias', [0.0951296, 0.139596], ...
'final_losses', [18.784, 18.8665, 17.2611], ...
'train_times', [310.199, 305.467, 348.852], ...
'min_feature_time', 0.055312, ...
'min_filter_time', 0.000852, ...
'median_feature_time', 0.288023, ...
'median_filter_time', 0.0065125, ...
'opt_traces', ...
  {{[2.38386e-05, 2.3836e-05, 2.3836e-05, 2.3836e-05, 2.3836e-05, 2.3836e-05, 2.3836e-05, 2.3836e-05], [2.33127e-05, 2.33102e-05, 2.33102e-05, 2.33102e-05], [2.29622e-05, 2.29595e-05, 2.29595e-05, 2.29595e-05, 2.29595e-05, 2.29595e-05, 2.29595e-05, 2.29595e-05]; ...
    [51.3942, 51.393, 51.3905, 51.3818, 51.3767, 51.3762, 51.3753, 51.3748, 51.3738, 51.3714, 51.366, 51.3627, 51.3557, 51.3539, 51.3529, 51.3518, 51.3511, 51.3506, 51.35, 51.3486, 51.347, 51.345, 51.3436, 51.3429, 51.3423, 51.3413, 39.0463, 36.3538, 29.795, 25.8872, 23.9196, 22.4947, 21.7881, 21.3804, 21.031, 20.6442, 20.3208, 19.9155, 19.5928, 19.4482, 19.3376, 19.2494, 19.1919, 19.0752, 18.9942, 18.9628, 18.9361, 18.9055, 18.8807, 18.8685, 18.8563, 18.8478, 18.8383, 18.8223, 18.8111, 18.8031, 18.7976, 18.794, 18.7909, 18.7886, 18.7864, 18.7853, 18.7848, 18.7845, 18.784], [50.2447, 50.2427, 50.2408, 50.2367, 50.2296, 50.2289, 50.2283, 50.2232, 50.218, 50.2129, 50.2102, 50.2068, 50.2058, 50.2042, 50.2036, 50.2029, 50.2015, 39.6105, 35.377, 29.7667, 25.9033, 23.6309, 23.0111, 22.5857, 22.0161, 21.8457, 21.2654, 20.8749, 20.5072, 20.3048, 20.0313, 19.7836, 19.6496, 19.4811, 19.3636, 19.2713, 19.2141, 19.1714, 19.1358, 19.1126, 19.0912, 19.064, 19.0371, 19.0151, 18.9971, 18.9661, 18.9488, 18.9403, 18.9358, 18.9293, 18.9198, 18.9127, 18.9069, 18.9021, 18.896, 18.8889, 18.8838, 18.8808, 18.8782, 18.8756, 18.8723, 18.8705, 18.8688, 18.8676, 18.8665], [49.4617, 49.4609, 49.4558, 49.4448, 49.4442, 49.4437, 49.4428, 49.4423, 49.4419, 49.4417, 49.4414, 49.441, 49.4403, 49.4392, 49.4376, 49.4357, 35.7343, 30.6722, 26.3111, 23.8507, 21.7312, 20.7321, 20.5965, 20.2255, 19.8907, 19.2776, 18.7232, 18.4286, 18.1216, 17.9284, 17.7975, 17.6841, 17.5668, 17.5132, 17.4412, 17.3982, 17.3836, 17.3697, 17.3442, 17.328, 17.3193, 17.3092, 17.2995, 17.2935, 17.2906, 17.2882, 17.2845, 17.2801, 17.2773, 17.2753, 17.274, 17.272, 17.2702, 17.2695, 17.2688, 17.2677, 17.2663, 17.2655, 17.2649, 17.2642, 17.2634, 17.2625, 17.262, 17.2615, 17.2611]}} ...
);

% Tuning error = 1.522775
% i_pass = 2, i_var = 3, i_mult = 2
% Hyperparams written at 2017-06-05, 03:18:47:767
