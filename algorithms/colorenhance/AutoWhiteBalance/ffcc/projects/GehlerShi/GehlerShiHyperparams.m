function [params, metrics] = GehlerShiHyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-08-09, 10:25:25:166.

params.HYPERPARAMS = struct( ...
'VONMISES_MULTIPLIER', 2^2.5, ...
'CROSSENT_MULTIPLIER', 2^-15.5, ...
'FILTER_SHIFTS', [2^-39.5, 2^-40], ...
'FILTER_MULTIPLIERS', [2^-23.5, 2^-25], ...
'BIAS_SHIFT', 2^-40.25, ...
'BIAS_MULTIPLIER', 2^-23, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 1.84397, ...
    'mean2', 2.89248, ...
    'mean4', 5.11884, ...
    'median', 0.974572, ...
    'tri', 1.20774, ...
    'b25', 0.28521, ...
    'w25', 4.75245, ...
    'w05', 9.2751, ...
    'max', 16.2501 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 2.40755, ...
    'mean2', 3.71732, ...
    'mean4', 6.52144, ...
    'median', 1.33933, ...
    'tri', 1.64688, ...
    'b25', 0.405924, ...
    'w25', 6.12592, ...
    'w05', 11.6245, ...
    'max', 22.1323 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 2.06521, ...
    'mean2', 3.60506, ...
    'mean4', 8.33739, ...
    'median', 1.18243, ...
    'tri', 1.31722, ...
    'b25', 0.437568, ...
    'w25', 5.24607, ...
    'w05', 12.129, ...
    'max', 36.3892 ...
)    , ...
'uv_bin_bias', [-0.0254305, 0.039971], ...
'final_losses', [2897.05, 2778.8, 2682.88], ...
'train_times', [83.0161, 64.2579, 65.5368], ...
'min_feature_time', 0.017385, ...
'min_filter_time', 0.000698, ...
'median_feature_time', 0.02158, ...
'median_filter_time', 0.000829, ...
'opt_traces', ...
  {{[0.0680269, 0.0670676, 0.0670671, 0.0670671, 0.0670671, 0.0670671, 0.0670671, 0.0670671], [0.067668, 0.0666838, 0.0666833, 0.0666833, 0.0666833, 0.0666833, 0.0666833], [0.0682064, 0.0672387, 0.0672383, 0.0672383, 0.0672383, 0.0672383, 0.0672383]; ...
    [12533.6, 10317.9, 7807.25, 6716.2, 5681.26, 5119.51, 4852.71, 4525.52, 4303.46, 4139.16, 4112.9, 4024.44, 3988, 3922.82, 3846.88, 3694.89, 3628.19, 3591.01, 3562.25, 3496.46, 3458.08, 3412.62, 3357.17, 3334.31, 3320.23, 3304.24, 3272.98, 3245.21, 3211.33, 3188.79, 3177.12, 3167.12, 3153.05, 3137.76, 3120.43, 3099.24, 3082.38, 3067.78, 3056.97, 3046.86, 3036.63, 3024.25, 3015.85, 3008.48, 2996.71, 2987.71, 2981.52, 2974.33, 2966.27, 2960.56, 2956.75, 2950.95, 2945.42, 2940.04, 2936.35, 2932.83, 2929.68, 2925.46, 2919.89, 2915.79, 2912.38, 2909.33, 2906.05, 2900.56, 2897.05], [12450.5, 7725.03, 7267.19, 5625.03, 5038.68, 4777.4, 4404.19, 4184, 3991.66, 3899.43, 3774.9, 3684.42, 3595.57, 3536.46, 3435.91, 3394.99, 3336.4, 3294.9, 3264.71, 3248.78, 3234.88, 3207.81, 3162.26, 3141.68, 3122.42, 3113.56, 3099.59, 3078.24, 3060.69, 3036.19, 3023.81, 3010.84, 2991.35, 2984.32, 2970.54, 2963.32, 2955.04, 2942.57, 2932.79, 2925.65, 2917.04, 2910.65, 2896.98, 2886.88, 2875.53, 2866.31, 2859.17, 2853.7, 2849.52, 2844.92, 2836.8, 2830.16, 2818.76, 2812.4, 2808.52, 2805.28, 2801.48, 2796.94, 2792.93, 2789.62, 2786.94, 2785.17, 2784.09, 2782.01, 2778.8], [12568.8, 8409.66, 8123, 6319.95, 5551.99, 5130.98, 4580.66, 4349.19, 4298.79, 4193.84, 3986.62, 3883.21, 3784.78, 3646.38, 3550.7, 3472.75, 3445.12, 3412, 3377.07, 3319.66, 3265.21, 3204.78, 3161.56, 3127.4, 3114.49, 3102.7, 3085.67, 3060.69, 3011.84, 2990.68, 2965.99, 2954.77, 2932.9, 2910.98, 2893.38, 2871.16, 2856.19, 2843.1, 2825.78, 2812.88, 2802.47, 2795.44, 2789.53, 2780.77, 2768.07, 2758.31, 2752.3, 2746.46, 2738.76, 2730.45, 2724.72, 2720.29, 2717.25, 2713.33, 2708.35, 2704.62, 2701.98, 2697.94, 2695.37, 2693.42, 2690.9, 2689.18, 2686.79, 2684.68, 2682.88]}} ...
);

% Tuning error = 1.240865
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-08-09, 10:29:06:879
