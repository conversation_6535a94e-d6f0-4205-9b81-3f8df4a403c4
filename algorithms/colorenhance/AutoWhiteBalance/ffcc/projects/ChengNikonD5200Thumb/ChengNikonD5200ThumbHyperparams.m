% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengNikonD5200ThumbHyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:22:424.

params.HYPERPARAMS = struct( ...
'CROSSENT_MULTIPLIER', 2^-12.25, ...
'VONMISES_MULTIPLIER', 2^-2.25, ...
'FILTER_MULTIPLIERS', [2^-23, 2^-29.5], ...
'BIAS_MULTIPLIER', 2^-26, ...
'FILTER_SHIFTS', [2^-59.25, 2^-35.5], ...
'BIAS_SHIFT', 2^-51.5, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.29085, ...
    'mean2', 3.42002, ...
    'mean4', 5.79611, ...
    'median', 1.44565, ...
    'tri', 1.57225, ...
    'b25', 0.362185, ...
    'w25', 5.60835, ...
    'w05', 10.5385, ...
    'max', 15.5303 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 3.37803, ...
    'mean2', 5.01261, ...
    'mean4', 8.5271, ...
    'median', 2.22148, ...
    'tri', 2.43455, ...
    'b25', 0.581173, ...
    'w25', 8.18579, ...
    'w05', 15.5196, ...
    'max', 22.791 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 4.40768, ...
    'mean2', 12.8061, ...
    'mean4', 37.4859, ...
    'median', 2.02707, ...
    'tri', 2.21287, ...
    'b25', 0.781699, ...
    'w25', 12.6332, ...
    'w05', 38.2361, ...
    'max', 128.219 ...
)    , ...
'uv_bin_bias', [0.1729, -0.0266551], ...
'final_losses', [50.5858, 49.7486, 51.2933], ...
'train_times', [195.357, 165.01, 151.665], ...
'min_feature_time', 0.00164, ...
'min_filter_time', 0.000944, ...
'median_feature_time', 0.015901, ...
'median_filter_time', 0.0064865, ...
'opt_traces', ...
  {{[0.232235, 0.177986, 0.175717, 0.175447, 0.175437, 0.175437, 0.175437, 0.175437, 0.175437, 0.175437, 0.175437, 0.175437, 0.175437, 0.175437], [0.227112, 0.174007, 0.171874, 0.171587, 0.171574, 0.171574, 0.171574, 0.171574, 0.171574, 0.171574, 0.171574, 0.171574, 0.171574, 0.171574, 0.171574, 0.171574, 0.171574], [0.223697, 0.172649, 0.17055, 0.170298, 0.170287, 0.170287, 0.170287, 0.170287, 0.170287, 0.170287, 0.170287, 0.170287, 0.170287, 0.170287, 0.170287, 0.170287, 0.170287]; ...
    [150.402, 97.1634, 83.8178, 81.5088, 78.9152, 76.8743, 71.9809, 70.9037, 69.8421, 68.3152, 67.5037, 65.9536, 63.6243, 62.4782, 61.8614, 61.1427, 60.7332, 60.3158, 59.8985, 59.4179, 58.8747, 58.243, 57.8826, 57.5443, 57.2161, 56.6005, 56.1861, 55.8498, 55.5169, 55.1901, 54.7883, 54.4413, 54.099, 53.9282, 53.7465, 53.5245, 53.2995, 52.875, 52.6599, 52.51, 52.3123, 52.1832, 52.0107, 51.8857, 51.784, 51.7281, 51.6594, 51.5745, 51.4419, 51.3406, 51.2582, 51.2012, 51.1343, 51.066, 51.0108, 50.9792, 50.9532, 50.9191, 50.8797, 50.8302, 50.7764, 50.7363, 50.6904, 50.6451, 50.5858], [146.35, 100.445, 86.2474, 83.0126, 76.178, 72.7984, 71.041, 69.3967, 68.2842, 65.8299, 64.7033, 63.6552, 62.7053, 61.6805, 60.8528, 60.1281, 59.3783, 58.9301, 58.1401, 57.7439, 57.4099, 57.1652, 56.6947, 56.1097, 55.6419, 55.3795, 55.1691, 54.8515, 54.2961, 53.9367, 53.5967, 53.2016, 52.9161, 52.616, 52.3473, 52.1239, 51.9825, 51.8007, 51.5853, 51.4011, 51.22, 51.126, 50.9877, 50.8348, 50.7251, 50.6545, 50.6028, 50.526, 50.4748, 50.4182, 50.3436, 50.2379, 50.1389, 50.0761, 50.0326, 49.9946, 49.9458, 49.9148, 49.8928, 49.8624, 49.8366, 49.8116, 49.7891, 49.7691, 49.7486], [144.781, 97.9799, 85.4884, 82.823, 76.3024, 75.1854, 74.401, 71.1434, 69.345, 68.5682, 67.2453, 66.0856, 65.1594, 64.6209, 64.0864, 63.1375, 61.9958, 60.7559, 60.1867, 59.7425, 59.3692, 59.1515, 58.4782, 57.9617, 57.5294, 57.1404, 56.7778, 56.3305, 56.1264, 55.5704, 55.1266, 54.7253, 54.3952, 54.205, 54.0256, 53.9134, 53.7396, 53.5513, 53.3869, 53.2447, 53.0768, 52.9063, 52.7369, 52.626, 52.5778, 52.515, 52.4169, 52.2337, 52.0724, 52.0342, 51.9419, 51.9163, 51.8332, 51.752, 51.6843, 51.6336, 51.5897, 51.549, 51.5162, 51.479, 51.4374, 51.3933, 51.3533, 51.3206, 51.2933]}} ...
);

% Tuning error = 1.602764
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-06-04, 21:30:10:050
