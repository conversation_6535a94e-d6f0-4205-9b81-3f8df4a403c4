% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengSamsungNX2000ThumbHyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:00:902.

params.HYPERPARAMS = struct( ...
'CROSSENT_MULTIPLIER', 2^-7, ...
'VONMISES_MULTIPLIER', 2^-2, ...
'FILTER_MULTIPLIERS', [2^-25.25, 2^-25], ...
'BIAS_MULTIPLIER', 2^-22.5, ...
'FILTER_SHIFTS', [2^-36.75, 2^-32], ...
'BIAS_SHIFT', 2^-37.5, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.24661, ...
    'mean2', 3.31026, ...
    'mean4', 5.91246, ...
    'median', 1.46721, ...
    'tri', 1.63227, ...
    'b25', 0.406855, ...
    'w25', 5.38749, ...
    'w05', 9.74013, ...
    'max', 19.8044 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 3.19944, ...
    'mean2', 4.6425, ...
    'mean4', 7.84585, ...
    'median', 2.01568, ...
    'tri', 2.25512, ...
    'b25', 0.638619, ...
    'w25', 7.59026, ...
    'w05', 14.0802, ...
    'max', 23.6962 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 3.32554, ...
    'mean2', 7.93515, ...
    'mean4', 22.1537, ...
    'median', 1.90111, ...
    'tri', 2.03164, ...
    'b25', 0.903625, ...
    'w25', 8.39923, ...
    'w05', 24.3091, ...
    'max', 70.4778 ...
)    , ...
'uv_bin_bias', [0.0888251, 0.0875758], ...
'final_losses', [72.7745, 72.7123, 71.9938], ...
'train_times', [108.411, 166.619, 198.688], ...
'min_feature_time', 0.001547, ...
'min_filter_time', 0.000892, ...
'median_feature_time', 0.0027495, ...
'median_filter_time', 0.0064515, ...
'opt_traces', ...
  {{[8.96759, 5.63656, 5.36595, 5.14699, 5.10072, 5.09086, 5.07326, 5.07193, 5.07081, 5.07072, 5.07071, 5.07071, 5.07071, 5.07071, 5.07071, 5.07071, 5.07071], [8.70766, 5.41298, 5.17409, 4.98111, 4.93217, 4.92781, 4.90653, 4.90529, 4.90423, 4.90414, 4.90412, 4.90412, 4.90412, 4.90412, 4.90412, 4.90412, 4.90412], [8.5777, 5.4053, 5.15846, 4.98016, 4.93905, 4.93026, 4.91222, 4.91126, 4.91053, 4.91042, 4.9104, 4.9104, 4.9104, 4.9104, 4.9104, 4.9104, 4.9104]; ...
    [121.148, 100.994, 96.4797, 91.1866, 87.1558, 84.7563, 82.912, 82.0743, 81.204, 80.5525, 79.0146, 77.8891, 77.2203, 76.7352, 76.4682, 76.0158, 75.5587, 75.1907, 74.9135, 74.6477, 74.4259, 74.2635, 74.1137, 73.9742, 73.7846, 73.6471, 73.5513, 73.4708, 73.3822, 73.2795, 73.1507, 73.0637, 73.0161, 72.9934, 72.9571, 72.9261, 72.9081, 72.8971, 72.885, 72.8748, 72.8663, 72.861, 72.8552, 72.8484, 72.8416, 72.8351, 72.8278, 72.8185, 72.807, 72.7985, 72.7924, 72.7883, 72.7836, 72.7811, 72.779, 72.7772, 72.776, 72.7755, 72.7752, 72.7749, 72.7748, 72.7747, 72.7746, 72.7746, 72.7745], [118.688, 108.378, 98.1695, 93.6231, 91.7723, 89.4656, 85.4676, 82.057, 80.9136, 80.1718, 79.5852, 78.9068, 78.0177, 77.3309, 76.7192, 76.1554, 75.7333, 75.473, 75.194, 74.8558, 74.6716, 74.5746, 74.44, 74.2226, 74.0904, 74.038, 73.9728, 73.8658, 73.7457, 73.6388, 73.5214, 73.4719, 73.4091, 73.3036, 73.2274, 73.1219, 73.0185, 72.9517, 72.9048, 72.8843, 72.8641, 72.8305, 72.793, 72.7571, 72.7487, 72.7365, 72.7343, 72.7309, 72.7277, 72.7231, 72.7199, 72.7181, 72.7175, 72.7166, 72.7154, 72.7146, 72.7143, 72.7141, 72.7138, 72.7134, 72.713, 72.7127, 72.7125, 72.7124, 72.7123], [119.099, 107.034, 96.7916, 91.8759, 89.0009, 85.7577, 83.282, 81.4228, 80.6724, 79.6664, 78.664, 77.1228, 76.2407, 75.7614, 75.3692, 75.1051, 74.9656, 74.6859, 74.242, 73.944, 73.7162, 73.5341, 73.3993, 73.2126, 73.0525, 72.9738, 72.9256, 72.84, 72.7408, 72.668, 72.6138, 72.5515, 72.5022, 72.4749, 72.4512, 72.4327, 72.4066, 72.3785, 72.3618, 72.3509, 72.3292, 72.31, 72.291, 72.2754, 72.2684, 72.2504, 72.22, 72.2034, 72.1892, 72.1735, 72.1603, 72.1347, 72.1227, 72.1011, 72.08, 72.066, 72.0593, 72.0459, 72.0335, 72.0245, 72.0122, 72.0068, 72.0033, 71.9986, 71.9938]}} ...
);

% Tuning error = 1.638052
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-06-04, 21:29:12:454
