% Copyright 2017 Google Inc.
% 
% Licensed under the Apache License, Version 2.0 (the "License");
% you may not use this file except in compliance with the License.
% You may obtain a copy of the License at
% 
%     https://www.apache.org/licenses/LICENSE-2.0
% 
% Unless required by applicable law or agreed to in writing, software
% distributed under the License is distributed on an "AS IS" BASIS,
% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
% See the License for the specific language governing permissions and
% limitations under the License.

function [params, metrics] = ChengNikonD5200Hyperparams(params)
% The hyperparameters for this project, produced using Tune(). See
% ../DefaultHyperparams.m for documentation.
% Tuning started at 2017-06-04, 21:21:22:280.

params.HYPERPARAMS = struct( ...
'VONMISES_MULTIPLIER', 2^-1.75, ...
'CROSSENT_MULTIPLIER', 2^-7, ...
'FILTER_SHIFTS', [2^-42.75, 2^-89.75], ...
'FILTER_MULTIPLIERS', [2^-24.5, 2^-29], ...
'BIAS_SHIFT', 2^-70, ...
'BIAS_MULTIPLIER', 2^-23.25, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 2.39988, ...
    'mean2', 3.66644, ...
    'mean4', 6.44714, ...
    'median', 1.41369, ...
    'tri', 1.61909, ...
    'b25', 0.459093, ...
    'w25', 5.94686, ...
    'w05', 11.861, ...
    'max', 19.2342 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 3.4929, ...
    'mean2', 5.33405, ...
    'mean4', 9.37227, ...
    'median', 2.07316, ...
    'tri', 2.38744, ...
    'b25', 0.696665, ...
    'w25', 8.64294, ...
    'w05', 17.1652, ...
    'max', 27.2517 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 3.68957, ...
    'mean2', 7.03584, ...
    'mean4', 14.8671, ...
    'median', 1.70314, ...
    'tri', 1.97551, ...
    'b25', 0.808214, ...
    'w25', 10.1388, ...
    'w05', 25.524, ...
    'max', 46.936 ...
)    , ...
'uv_bin_bias', [0.316544, 0.133832], ...
'final_losses', [70.2083, 69.7902, 72.6984], ...
'train_times', [196.688, 168.33, 251.97], ...
'min_feature_time', 0.05966, ...
'min_filter_time', 0.000788, ...
'median_feature_time', 0.311656, ...
'median_filter_time', 0.0011825, ...
'opt_traces', ...
  {{[8.83763, 6.82211, 6.27531, 5.14123, 4.93247, 4.64263, 4.52424, 4.48283, 4.44319, 4.42838, 4.4215, 4.41797, 4.41572, 4.41467, 4.41424, 4.41395, 4.41377], [8.64268, 6.45348, 4.94758, 4.79909, 4.65537, 4.42299, 4.36742, 4.33564, 4.31623, 4.30864, 4.30399, 4.3009, 4.29939, 4.2988, 4.29855, 4.29851, 4.2985], [8.51271, 6.34696, 5.88099, 4.71249, 4.57098, 4.47686, 4.39466, 4.36569, 4.32435, 4.30299, 4.29915, 4.29797, 4.29656, 4.29574, 4.29532, 4.29527, 4.29526]; ...
    [114.943, 112.562, 108.07, 105.256, 103.753, 98.6844, 96.0313, 93.6072, 92.5099, 90.6936, 88.8607, 86.8788, 85.5597, 84.6578, 82.7039, 81.0118, 80.0082, 78.6016, 78.1177, 77.577, 76.9761, 76.0258, 75.2883, 74.6211, 74.2737, 74.1084, 73.813, 73.6131, 73.2899, 73.0643, 72.8987, 72.7011, 72.4401, 72.1443, 72.0019, 71.9288, 71.8457, 71.7299, 71.5868, 71.4756, 71.3913, 71.3122, 71.2063, 71.1138, 70.9924, 70.8922, 70.8273, 70.8038, 70.7635, 70.7391, 70.7039, 70.6648, 70.6132, 70.5712, 70.5066, 70.4588, 70.4232, 70.4, 70.3729, 70.3217, 70.2917, 70.2624, 70.2372, 70.2216, 70.2083], [110.946, 110.51, 105.353, 102.169, 100.349, 98.1988, 95.2492, 93.2746, 91.8989, 89.2912, 87.8709, 85.5807, 83.4489, 82.0501, 81.5164, 81.2414, 80.4597, 79.3748, 78.1712, 77.3876, 76.9289, 76.6673, 76.0767, 75.4076, 74.8683, 74.5835, 74.2365, 73.9496, 73.3916, 73.1289, 72.9535, 72.6708, 72.4772, 72.2793, 72.072, 71.8622, 71.5024, 71.3161, 71.1629, 71.0382, 70.9301, 70.7991, 70.7133, 70.6503, 70.5958, 70.5259, 70.4209, 70.3218, 70.2404, 70.1964, 70.1488, 70.0598, 70.0224, 69.9851, 69.9681, 69.9501, 69.9171, 69.8927, 69.8784, 69.8671, 69.8518, 69.8302, 69.8094, 69.8001, 69.7902], [112.411, 111.276, 108.113, 105.932, 104.133, 100.511, 97.2419, 96.3336, 92.6251, 92.215, 90.767, 89.5327, 88.3482, 87.5266, 86.5728, 85.4078, 84.1574, 81.9114, 80.958, 80.3673, 79.5016, 78.9695, 78.3225, 77.8165, 77.4535, 77.0993, 76.6408, 76.2849, 75.9391, 75.6051, 75.3518, 75.1869, 74.8424, 74.5702, 74.391, 74.2812, 74.1813, 74.0382, 73.9262, 73.7557, 73.7363, 73.734, 73.7183, 73.6914, 73.629, 73.5288, 73.5037, 73.4768, 73.4118, 73.3195, 73.29, 73.2577, 73.2383, 73.2251, 73.1811, 73.152, 73.1093, 73.0654, 73.0112, 72.9462, 72.9002, 72.8708, 72.8185, 72.7649, 72.6984]}} ...
);

% Tuning error = 1.718703
% i_pass = 1, i_var = 1, i_mult = 1
% Hyperparams written at 2017-06-04, 21:32:03:750
