function params = load_params()
% Have cross-validation minimize the error metric minimized in Barron's
% "Convolutional Color Constancy" 2015 paper, which is a good choice for
% producing low-error models.
params.TRAINING.TUNING_ERROR_FUNCTION = @(x)geomean([x.rgb_err.mean, ...
  x.rgb_err.median, x.rgb_err.tri, x.rgb_err.b25, x.rgb_err.w25]);
% If true, a black body per-bin prior gets learned. Set to
% true if the photometric properties of your input are consistent (ie, all
% images will come from the same camera) and set to false otherwise (ie,
% you would like to run on arbitrary images from the internet).
params.TRAINING.LEARN_BIAS = true;

% The number of bins in the histogram, which is also the size of the FFT
% convolutions that must be performed.
params.HISTOGRAM.NUM_BINS = 256;
% The spacing between bins in UV coordinates, which determines the resolution
% of the predicted UV white points.
params.HISTOGRAM.BIN_SIZE = 1/64;
% The UV coordinate of the first bin in the histogram. Here it's set such
% that the center of the histogram roughly corresponds to a gain of [2, 1, 2],
% which is standard for cameras where green is gained up by 2x.
params.HISTOGRAM.STARTING_UV = -1.421875;
% Whether or not to ignore zero-valued pixels when constructing histograms
% from training data. This is a good idea if the training data contains
% color charts or saturated pixels which have been masked out.
params.HISTOGRAM.MASK_ZERO_PIXELS = true;

% The expected resolution of the sensor stats.
params.SENSOR.STATS_SIZE = [256, 384];
% The expected bit depth of the feature stats, 12 bit 2.7mp images downsampled
% to 0.1mp images should give at least 4 extra bits of precision.
params.SENSOR.STATS_BIT_DEPTH = 16;
% Setting the CCM to nan will cause the importing code to use whatever
% input CCM is correct.
params.SENSOR.CCM = nan;
% If true, assume that the input stats are linear intensity. Otherwise, assume
% that the input stats are in gamma-corrected sRGB space.
params.SENSOR.LINEAR_STATS = true;

params.HYPERPARAMS = struct( ...
'VONMISES_MULTIPLIER', 2^2.5, ...
'CROSSENT_MULTIPLIER', 2^-15.5, ...
'FILTER_SHIFTS', [2^-39.5], ...
'FILTER_MULTIPLIERS', [2^-23.5], ...
'BIAS_SHIFT', 2^-40.25, ...
'BIAS_MULTIPLIER', 2^-23, ...
'VON_MISES_DIAGONAL_EPS', 2^0 ...
);

% 'FILTER_SHIFTS', [2^-39.5, 2^-40], ...
% 'FILTER_MULTIPLIERS', [2^-23.5, 2^-25], ...

% The parameters control how many passes of LBFGS we perform as the loss
% function is annealed from convex option to non-convex. The number of
% iterations for each pass of LBFGS is controlled by the "INITIAL" and
% "FINAL" constants, such that the number of iterations for a pass is
% produced by linearly interpolating between those two values in log-space.
% Here we are using a subset of this functionality where we perform
% two passes of optimization, one to pretrain with the convex loss and the
% second to fine-tune with the non-convex loss.
params.TRAINING.NUM_ITERS_ANNEAL = 2;
params.TRAINING.NUM_ITERS_LBFGS_INITIAL = 128;
params.TRAINING.NUM_ITERS_LBFGS_FINAL = 256;

% If true, use bilinear interpolation to construct the ground-truth PDF used in
% the pretraining loss.
params.TRAINING.SMOOTH_CROSS_ENTROPY = true;

% A value that is added to the diagonal of the covariance matrix in the fitted
% Von Mises distributions when evaluating and training our model, in units of
% histogram bins. Setting this to 1 seems to work well, though analytically this
% should be 1/12, which is the variance of the histogram being sampled.
params.HISTOGRAM.VON_MISES_DIAGONAL_MODE = 'pad';

% Whether or not to print the LBFGS output during optimization.
params.DEBUG.DISPLAY_LBFGS = true;

% The loss imposed on the von Mises non-pretraining stage.
% Valid options: 'likelihood', 'squared_error', 'expected_squared_error'
params.TRAINING.VON_MISES_LOSS = 'likelihood';

% If true, constrains the von mises distributions fitted during training and
% testing to be isotropic (ie, their convariance matrices are scaled identity
% matrices).
params.TRAINING.FORCE_ISOTROPIC_VON_MISES = false;

% If true, confirm that the preconditioner is an accurate Jacobi preconditioner
% of the regularizer, by printing out a series of errors which should be close
% close to zero.
params.DEBUG.CORRECT_PRECONDITIONER = false;

metrics = struct( ...
'rgb_err', ...
  struct( ...
    'mean', 1.84397, ...
    'mean2', 2.89248, ...
    'mean4', 5.11884, ...
    'median', 0.974572, ...
    'tri', 1.20774, ...
    'b25', 0.28521, ...
    'w25', 4.75245, ...
    'w05', 9.2751, ...
    'max', 16.2501 ...
)    , ...
'uv_err', ...
  struct( ...
    'mean', 2.40755, ...
    'mean2', 3.71732, ...
    'mean4', 6.52144, ...
    'median', 1.33933, ...
    'tri', 1.64688, ...
    'b25', 0.405924, ...
    'w25', 6.12592, ...
    'w05', 11.6245, ...
    'max', 22.1323 ...
)    , ...
'vonmises_nll', ...
  struct( ...
    'mean', 2.06521, ...
    'mean2', 3.60506, ...
    'mean4', 8.33739, ...
    'median', 1.18243, ...
    'tri', 1.31722, ...
    'b25', 0.437568, ...
    'w25', 5.24607, ...
    'w05', 12.129, ...
    'max', 36.3892 ...
)    , ...
'uv_bin_bias', [-0.0254305, 0.039971], ...
'final_losses', [2897.05, 2778.8, 2682.88], ...
'train_times', [83.0161, 64.2579, 65.5368], ...
'min_feature_time', 0.017385, ...
'min_filter_time', 0.000698, ...
'median_feature_time', 0.02158, ...
'median_filter_time', 0.000829, ...
'opt_traces', ...
  {{[0.0680269, 0.0670676, 0.0670671, 0.0670671, 0.0670671, 0.0670671, 0.0670671, 0.0670671], [0.067668, 0.0666838, 0.0666833, 0.0666833, 0.0666833, 0.0666833, 0.0666833], [0.0682064, 0.0672387, 0.0672383, 0.0672383, 0.0672383, 0.0672383, 0.0672383]; ...
    [12533.6, 10317.9, 7807.25, 6716.2, 5681.26, 5119.51, 4852.71, 4525.52, 4303.46, 4139.16, 4112.9, 4024.44, 3988, 3922.82, 3846.88, 3694.89, 3628.19, 3591.01, 3562.25, 3496.46, 3458.08, 3412.62, 3357.17, 3334.31, 3320.23, 3304.24, 3272.98, 3245.21, 3211.33, 3188.79, 3177.12, 3167.12, 3153.05, 3137.76, 3120.43, 3099.24, 3082.38, 3067.78, 3056.97, 3046.86, 3036.63, 3024.25, 3015.85, 3008.48, 2996.71, 2987.71, 2981.52, 2974.33, 2966.27, 2960.56, 2956.75, 2950.95, 2945.42, 2940.04, 2936.35, 2932.83, 2929.68, 2925.46, 2919.89, 2915.79, 2912.38, 2909.33, 2906.05, 2900.56, 2897.05], [12450.5, 7725.03, 7267.19, 5625.03, 5038.68, 4777.4, 4404.19, 4184, 3991.66, 3899.43, 3774.9, 3684.42, 3595.57, 3536.46, 3435.91, 3394.99, 3336.4, 3294.9, 3264.71, 3248.78, 3234.88, 3207.81, 3162.26, 3141.68, 3122.42, 3113.56, 3099.59, 3078.24, 3060.69, 3036.19, 3023.81, 3010.84, 2991.35, 2984.32, 2970.54, 2963.32, 2955.04, 2942.57, 2932.79, 2925.65, 2917.04, 2910.65, 2896.98, 2886.88, 2875.53, 2866.31, 2859.17, 2853.7, 2849.52, 2844.92, 2836.8, 2830.16, 2818.76, 2812.4, 2808.52, 2805.28, 2801.48, 2796.94, 2792.93, 2789.62, 2786.94, 2785.17, 2784.09, 2782.01, 2778.8], [12568.8, 8409.66, 8123, 6319.95, 5551.99, 5130.98, 4580.66, 4349.19, 4298.79, 4193.84, 3986.62, 3883.21, 3784.78, 3646.38, 3550.7, 3472.75, 3445.12, 3412, 3377.07, 3319.66, 3265.21, 3204.78, 3161.56, 3127.4, 3114.49, 3102.7, 3085.67, 3060.69, 3011.84, 2990.68, 2965.99, 2954.77, 2932.9, 2910.98, 2893.38, 2871.16, 2856.19, 2843.1, 2825.78, 2812.88, 2802.47, 2795.44, 2789.53, 2780.77, 2768.07, 2758.31, 2752.3, 2746.46, 2738.76, 2730.45, 2724.72, 2720.29, 2717.25, 2713.33, 2708.35, 2704.62, 2701.98, 2697.94, 2695.37, 2693.42, 2690.9, 2689.18, 2686.79, 2684.68, 2682.88]}} ...
);

