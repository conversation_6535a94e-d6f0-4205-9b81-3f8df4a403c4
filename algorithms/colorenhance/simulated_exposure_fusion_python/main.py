import numpy as np
from skimage import img_as_float32, img_as_ubyte, io
import time, os

import sef
import clahe
import sat

def find_images_in_folder(folder_path):
    image_extensions = {'png', 'jpg', 'jpeg', 'bmp', 'gif', 'tif', 'tiff'}
    image_files = []

    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith(tuple(image_extensions)):
                image_files.append(os.path.join(root, file))

    return image_files


if __name__ == "__main__":
    folder_path = '/home/<USER>/code/IEDemo/algorithms/colorenhance/Image-Adaptive-3DLUT/demo_images/sRGB'
    output_path = '/home/<USER>/data/imagesresults/sef2'
    clahe_space = "yuv"   # yuv    hsv     hsl    lab
    saturation = 1.2
    clip_limit = 1.0
    tile_grid_size=(1, 1)

    if not os.path.exists(output_path):
        os.makedirs(output_path)

    images = find_images_in_folder(folder_path)
    for image_file in images:
        print(f"{image_file} --> {output_path}")
        img = img_as_float32(io.imread(image_file))
        M=None
        n_scales=0
        alpha=2   
        beta=0.8   
        lambd=0.125
        S_black=0
        S_white=0
        sef_times = 1

        """
        '- alpha  : maximal contrast factor (recommended: 6)\n' ...
        '- beta   : reduced dynamic range (recommended: 0.5)\n' ...
        '- nScales: number of scales: n, or 0 or -1 or -2 for auto setting (recommended: 0)\n' ...
        '           -  n: use n scales\n' ...
        '           -  0: use Mertens et al. pyramid depth\n' ...
        '           - -1: use a deeper pyramid (smallest dim has size 1 at last scale)\n' ...
        '           - -2: use the deepest pyramid (largest dim has size 1 at last scale)\n' ...
        '- bClip  : maximal percentage of white-saturated pixels (recommended: 1)\n' ...
        '- wClip  : maximal percentage of black-saturated pixels (recommended: 1)\n' ...
        """

        # filename
        file_name = image_file.split("/")[-1].split(".")[0]

        # sef
        img_sef = img.copy()
        for i in range(sef_times):
            img_sef = sef.simulated_exposure_fusion(img_sef, M, n_scales, alpha, beta, lambd, S_black, S_white)
        img_sef = img_sef * 255
        img_sef = img_sef.clip(0, 255).astype(np.uint8)
        outimage_path = os.path.join(output_path, file_name + f"-alpha{alpha}beta{beta}s{S_black}{S_white}t{sef_times}.png")
        io.imsave(outimage_path, img_as_ubyte(img_sef))

        # sef + saturation
        img_sef_sat = clahe.clahe_saturation(img_sef, saturation)
        # img_sef_sat = sat.adjust_green(img_sef, saturation)
        outimage_path = os.path.join(output_path, file_name + f"-alpha{alpha}beta{beta}s{S_black}{S_white}t{sef_times}sat{saturation}.png")
        io.imsave(outimage_path, img_as_ubyte(img_sef_sat))

        # sef + clahe
        if clahe_space == "yuv":
            img_sef_clahe = clahe.clahe_image_yuv(img_sef, clip_limit, tile_grid_size)
        if clahe_space == "hsv":
            img_sef_clahe = clahe.clahe_image_hsv(img_sef, clip_limit, tile_grid_size)
        if clahe_space == "hsl":
            img_sef_clahe = clahe.clahe_image_hsl(img_sef, clip_limit, tile_grid_size)
        if clahe_space == "lab":
            img_sef_clahe = clahe.clahe_image_lab(img_sef, clip_limit, tile_grid_size)

        outimage_path = os.path.join(output_path, file_name + f"-alpha{alpha}beta{beta}s{S_black}{S_white}t{sef_times}clahe{clip_limit}{clahe_space}{tile_grid_size[0]}.png")
        io.imsave(outimage_path, img_as_ubyte(img_sef_clahe))


        # sef + clahe + saturation
        img_sef_clahe_sat = clahe.clahe_saturation(img_sef_clahe, saturation)
        outimage_path = os.path.join(output_path, file_name + f"-alpha{alpha}beta{beta}s{S_black}{S_white}t{sef_times}clahe{clip_limit}{clahe_space}{tile_grid_size[0]}sat{saturation}.png")
        io.imsave(outimage_path, img_as_ubyte(img_sef_clahe_sat))


