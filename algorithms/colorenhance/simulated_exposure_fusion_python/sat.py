

import cv2
import numpy as np



def adjust_green(image, saturation_factor):
    # to hsv
    hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)


    # OpenCV hue = 0-179
    # green = [60, 120]
    lower_green_hue = 40  
    upper_green_hue = 60

    # lower and upper
    lower_green = np.array([0, 0, 0])
    upper_green = np.array([255, 255, 255])

    # only keep green pixels
    mask = cv2.inRange(hsv_image, lower_green, upper_green)

    # adjust saturation 
    hsv_image[:, :, 1] = np.where(mask == 255, hsv_image[:, :, 1] * saturation_factor, hsv_image[:, :, 1])

    # to bgr
    result_image = cv2.cvtColor(hsv_image, cv2.COLOR_HSV2BGR)

    return result_image
