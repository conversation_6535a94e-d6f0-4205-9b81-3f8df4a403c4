import cv2
import numpy as np

def clahe_image_yuv(image, clip_limit, tile_grid_size):
    yuv_image = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
    yuv_image[:,:,0] = clahe.apply(yuv_image[:,:,0])
    equalized_image = cv2.cvtColor(yuv_image, cv2.COLOR_YUV2BGR)
    return equalized_image

def clahe_image_hsv(image, clip_limit, tile_grid_size):
    hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
    hsv_image[:,:,2] = clahe.apply(hsv_image[:,:,2])
    equalized_image = cv2.cvtColor(hsv_image, cv2.COLOR_HSV2BGR)
    return equalized_image

def clahe_image_hsl(image, clip_limit, tile_grid_size):
    hsl_image = cv2.cvtColor(image, cv2.COLOR_BGR2HLS_FULL)
    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
    hsl_image[:,:,1] = clahe.apply(hsl_image[:,:,1])
    equalized_image = cv2.cvtColor(hsl_image, cv2.COLOR_HLS2BGR_FULL)
    return equalized_image

def clahe_image_lab(image, clip_limit, tile_grid_size):
    hsl_image = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
    hsl_image[:,:,0] = clahe.apply(hsl_image[:,:,0])
    equalized_image = cv2.cvtColor(hsl_image, cv2.COLOR_LAB2BGR)
    return equalized_image


def clahe_saturation(image, saturation):
    hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    hsv_image = hsv_image.astype(np.float16)
    hsv_image[:,:,1] = hsv_image[:,:,1] * saturation
    hsv_image[:,:,1] = hsv_image[:,:,1] .clip(0, 255)
    hsv_image = hsv_image.astype(np.uint8)
    saturated_image = cv2.cvtColor(hsv_image, cv2.COLOR_HSV2BGR)
    return saturated_image