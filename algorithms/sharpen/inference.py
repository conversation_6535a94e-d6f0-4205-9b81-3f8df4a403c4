


import torch, cv2, random
from torch import nn
import torch.nn.functional as F
from torchvision import transforms
import numpy as np
from torch.utils.data import DataLoader
from networks import UNetV2, UNetV3
from datasetloader import   Sharpen_Dataset

import warnings
warnings.filterwarnings('ignore', category=DeprecationWarning)

def resume_checkpoint(model, resume_point, optimizer):  
    last_epoch= 0 
    if resume_point is not None:
        pretrained_model  = torch.load(resume_point)
        model.load_state_dict(pretrained_model)
        last_epoch = int(resume_point.split("_")[1][5:])
    return last_epoch

def get_loss_func():
    return nn.MSELoss()


def get_model(tag):
    model_tag = tag.split("_")[1]
    if model_tag == "UnetV2":
        return UNetV2()
    elif model_tag == "UnetV3":
        return UNetV3()
    else:
        raise ValueError("Invalid model tag {model_tag}")

def main(input_image_path):

    # 
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 
    inference_model = get_model(tag).to(device)

    # load mlp model 
    model_weights = torch.load(os.path.join(model_path, model_name), map_location=torch.device(device))   
    inference_model.load_state_dict(model_weights)    

    # 
    dataset = Sharpen_Dataset(input_image_path)
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=1)

    # 模型
    inference_model.eval()

    #for image, in dataloader:
    for batch_num, input_datain in enumerate(dataloader):
                
        # input, gt
        input_image  = input_datain[0]
        file_name    = input_datain[2]

        # 
        input_image = input_image.cuda()
        print(input_image.shape)

        # 前向传播
        sharpen_image = input_image.clone()
        hindex = 2
        windex = 3
        # Iterate through the image and save tiles
        for i in range(0, input_image.shape[hindex], patch_size):
            for j in range(0, input_image.shape[windex], patch_size):
                # print(f"inference tile ({i}, {j})")
                input_tile      = input_image[:, :, i:i + patch_size, j:j + patch_size]
                print(input_tile.shape)

                with torch.no_grad():
                    output_tile = inference_model(input_tile)
                sharpen_image[:, :, i:i + patch_size, j:j + patch_size] = output_tile
            input_tile = input_image[:, :, i:i + patch_size, (input_image.shape[windex] - patch_size):(input_image.shape[windex])]
            with torch.no_grad():
                output_tile = inference_model(input_tile)
            sharpen_image[:, :, i:i + patch_size, (input_image.shape[windex] - patch_size):(input_image.shape[windex])] = output_tile
        input_tile = input_image[:, :, (input_image.shape[hindex] - patch_size):(input_image.shape[hindex]), 
                                        (input_image.shape[windex] - patch_size):(input_image.shape[windex])]
        with torch.no_grad():
            output_tile = inference_model(input_tile)
        sharpen_image[:, :, (input_image.shape[hindex] - patch_size):(input_image.shape[hindex]), 
                            (input_image.shape[windex] - patch_size):(input_image.shape[windex])] = output_tile

        # log
        dump_output = sharpen_image[0].detach().cpu().numpy().transpose(1, 2, 0)
        result_name = f"{file_name[0].split('.')[0]}_{tag}.jpg"
        print(result_name)
        cv2.imwrite(os.path.join(test_result_path, result_name), dump_output * 255)
    


if __name__ == "__main__":
    import os
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128"

    if hasattr(torch.cuda, 'empty_cache'):
        torch.cuda.empty_cache()

    # fix seed 
    seed = 42
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.enabled = False

    model_path          = "/home/<USER>/modelhub/sharpen"
    test_result_path    = "/home/<USER>/project/sharpen/testdataset"

    # Define patch size
    patch_size = 256

    # projects
    tag = "SharpenV5big_UnetV2_inf256"
    model_name           = "SharpenV5_UnetV2_Epoch70_Loss0.1337.pth"
    input_image_path     = "/home/<USER>/data/dd_dp_dataset_canon/dd_dp_dataset_png/test_c/target"

    print(tag)
    if not os.path.exists(test_result_path): os.mkdir(test_result_path)
    main(input_image_path)