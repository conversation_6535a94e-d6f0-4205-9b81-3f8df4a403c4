import os

import cv2 as cv
import torch
from torch import nn
from torch.utils.data import Dataset
import torch.nn.functional as F
from torchvision import transforms



'--- UNet'
class DoubleConv(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(DoubleConv, self).__init__()
        self.layer = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU()
        )
    def forward(self, x):
        return self.layer(x)

class Down_DC(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(Down_DC, self).__init__()
        self.layer = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels)
        )
    def forward(self, x):
        return self.layer(x)

class Up_DC(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(Up_DC, self).__init__()
        self.dim_output = out_channels
        self.layer1 = F.interpolate
        self.layer2 = nn.Conv2d(in_channels, out_channels, kernel_size=1)
        self.layer3 = DoubleConv(in_channels, out_channels)
    def forward(self, x, x_shortcut):
        x = self.layer1(x, scale_factor=2)
        x = self.layer2(x)
        x = torch.cat((x_shortcut, x), dim=1)
        x = self.layer3(x)
        return x

class UNet(nn.Module):
    def __init__(self):
        super(UNet, self).__init__()
        self.inc = DoubleConv(3, 64)
        self.down1 = Down_DC(64, 128)
        self.down2 = Down_DC(128, 256)
        self.down3 = Down_DC(256, 512)
        self.down4 = Down_DC(512, 1024)
        self.up4 = Up_DC(1024, 512)
        self.up3 = Up_DC(512, 256)
        self.up2 = Up_DC(256, 128)
        self.up1 = Up_DC(128, 64)
        self.out = nn.Conv2d(64, 3, kernel_size=1)

    def forward(self, x):
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x = self.down4(x4)
        x = self.up4(x, x4)
        x = self.up3(x, x3)
        x = self.up2(x, x2)
        x = self.up1(x, x1)
        x = self.out(x)
        return x

class UNetV2(nn.Module):
    def __init__(self):
        super(UNetV2, self).__init__()
        self.inc = DoubleConv(3, 64)
        self.down1 = Down_DC(64, 128)
        self.down2 = Down_DC(128, 256)
        self.down3 = Down_DC(256, 512)
        self.down4 = Down_DC(512, 1024)
        self.up4 = Up_DC(1024, 512)
        self.up3 = Up_DC(512, 256)
        self.up2 = Up_DC(256, 128)
        self.up1 = Up_DC(128, 64)
        self.out = nn.Conv2d(64, 3, kernel_size=1)

    def forward(self, x):
        x0 = x
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x = self.down4(x4)
        x = self.up4(x, x4)
        x = self.up3(x, x3)
        x = self.up2(x, x2)
        x = self.up1(x, x1)
        x = self.out(x) + x0
        return x
    
class UNetV3(nn.Module):
    def __init__(self):
        super(UNetV3, self).__init__()
        self.inc = DoubleConv(3, 64)
        self.down1 = Down_DC(64, 128)
        self.down2 = Down_DC(128, 256)
        self.up2 = Up_DC(256, 128)
        self.up1 = Up_DC(128, 64)
        self.out = nn.Conv2d(64, 3, kernel_size=1)

    def forward(self, x):
        x0 = x
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x = self.down2(x2)
        x = self.up2(x, x2)
        x = self.up1(x, x1)
        x = self.out(x) + x0
        return x