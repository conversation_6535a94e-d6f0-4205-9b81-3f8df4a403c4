
import os
import cv2
from torch.utils.data import Dataset
from torchvision import transforms



    
class Sharpen_Dataset(Dataset):
    def __init__(self, input_image_path, sharp_image_path=None):
        super(Sharpen_Dataset, self).__init__()
        self.dir_input  = input_image_path
        self.dir_GT     = sharp_image_path
        self.files = os.listdir(self.dir_input)
        self.trans = transforms.Compose((
            transforms.ToTensor(),
        ))

    def __len__(self):
        return len(self.files)

    def __getitem__(self, item):
        file_name = self.files[item]
        input   = cv2.imread(os.path.join(self.dir_input, file_name))
        if self.dir_GT is None:
            return self.trans(input),  self.trans(input), file_name
        else:       
            GT = cv2.imread(os.path.join(self.dir_GT, file_name))
            return self.trans(input), self.trans(GT), file_name