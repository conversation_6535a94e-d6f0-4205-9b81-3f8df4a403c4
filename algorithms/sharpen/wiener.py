import numpy as np
from scipy.signal import convolve2d
from scipy.ndimage import fourier_gaussian
from PIL import Image

def wiener_filter(image, noise_var, kernel_size=5, kernel_sigma=None):
    """
    Apply a Wiener filter to an image.

    Parameters:
    - image: 2D numpy array (image)
    - noise_var: the variance of the noise
    - kernel_size: size of the kernel
    - kernel_sigma: sigma of the Gaussian kernel

    Returns:
    - filtered image
    """

    if kernel_sigma is None:
        kernel_sigma = kernel_size / 2

    # Create a 2D Gaussian kernel
    kernel = fourier_gaussian(kernel_size, kernel_sigma)
    kernel /= np.sum(kernel)  # Normalize the kernel

    # Convolve the kernel with the image
    padded_image = np.pad(image, kernel_size//2, mode='constant', constant_values=0)
    convolved_image = convolve2d(padded_image, kernel, 'same')

    # Estimate the power spectral density of the original image
    padded_image_fft = np.fft.fft2(padded_image)
    padded_image_fftshift = np.fft.fftshift(padded_image_fft)
    power_spectrum = np.abs(padded_image_fftshift)**2

    # Estimate the power spectral density of the noise
    noise_power_spectrum = noise_var * np.ones_like(power_spectrum)

    # Calculate the Wiener filter
    wiener_filter = power_spectrum / (power_spectrum + noise_power_spectrum)

    # Apply the Wiener filter
    filtered_image_fft = wiener_filter * np.fft.ifftshift(padded_image_fft)
    filtered_image = np.fft.ifft2(filtered_image_fft).real

    return filtered_image

# Example usage:
# Load your image as a 2D numpy array
# image = ...

# Assume you know the variance of the noise
# noise_var = ...

# Apply the Wiener filter
# filtered_image = wiener_filter(image, noise_var)



# if __name__ == '__main__':

#     # Load your image
#     image = Image.open('/home/<USER>/project/sharpen/gt50/1P0A0901-topazsharpen50.png').convert('L')
#     noise_var = 0.01

#     # Convert the image to a numpy array
#     image_array = np.array(image)

#     # Now you can apply the Wiener filter
#     filtered_image = wiener_filter(image_array, noise_var)



if __name__ == '__main__':
    import cv2
    import numpy as np
    from matplotlib import pyplot as plt

    # Load an image in grayscale mode
    image = cv2.imread('/home/<USER>/project/sharpen/gt50/1P0A0901-topazsharpen50.png', cv2.IMREAD_GRAYSCALE)

    # Check if image is loaded properly
    if image is None:
        raise ValueError("Could not open or find the image")

    # Apply Sobel operator in x direction
    sobelx = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)

    # Apply Sobel operator in y direction
    sobely = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)

    # Calculate the magnitude of the gradient
    sobel = np.sqrt(sobelx**2 + sobely**2)

    # Normalize the result to an 8-bit scale
    sobel = cv2.convertScaleAbs(sobel)

    edge_clip = np.clip(sobel, 0, 255).astype(np.float32) / 255.0
    # edge_clip = 1 - edge_clip
    edge_clip = 0.7 * edge_clip + 0.3

    shrinked = edge_clip * image
    
