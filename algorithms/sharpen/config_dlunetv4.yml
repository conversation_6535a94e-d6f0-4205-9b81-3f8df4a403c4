# configs 
pipeline_control:
  input_directory         : '/home/<USER>/data/dd_dp_dataset_canon/dd_dp_dataset_png/test_c/target'
  color_space             : 'rgb' # rgb/yuv/hsv
  upscaler                : 2 # 1/2/4/8
  upscaler_method         : 'lanczos' # Lanczos/bicubic/ecbsr
  downscaler_method       : 'bilinear' # bilinear/bicubic/nearest
  sharpen_method          : 'dlunetv2' # cv1usm/cv2wandcas/dlunetv2
  extra_tag               : 'v5' # any extra tag for the output file name
  suppress_color_noise    : 'none' # none/
  blending_amount         : 'all_100' # all_100/face/segmentation/blurbg
  output_directory        : '/home/<USER>/project/sharpen/testdataset'

sharpen_method_cv1sum:
  blur_filter             : "bilateralfilter"  # bilateralfilter/gaussianfilter/wlsfilter 
  blur_radius             : 2 # 1/2/3/4/5   
  blur_sigmaColor         : 200 # 100/200/300/400/500
  blur_sigmaSpace         : 200 # 100/200/300/400/500
  amount                  : 0.5 #  1/2/3/4/5

sharpen_method_cv2wandcas:
  wandradius              : 2
  wandsigma               : 2
  casamount               : 0.7

sharpen_method_dlunet:
  patch_size              : 256
  model_network           : 'UnetV2' # UnetV2
  model_path              : "/home/<USER>/modelhub/sharpen/SharpenV5_UnetV2_Epoch199_Loss0.0984.pth"
  inference_model         : ~  # NOT TO SET

