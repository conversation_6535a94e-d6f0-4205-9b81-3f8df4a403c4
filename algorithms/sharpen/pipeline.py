import cv2
import os, yaml, argparse
from wand.image import Image 
import torch
import numpy as np
from cas import contrast_adaptive_sharpening
from networks import UNetV2, UNetV3

# ## dataset
#     train: 
#         div2k
#         dd_canon 
#     test:
#         wild

# ## model 
#     train: 
#         GT = 
#             topaz sharpen35, 
#             ecbsr 1X,  
#             cvsharpen,
#         model = 
#             unet + ecb block
#     test:
#         unet + ecb block
#         cassharpen



# configs 
# input_directory         = '/home/<USER>/data/dd_dp_dataset_canon/dd_dp_dataset_png/test_c/target'
# color_space             = 'rgb' # rgb/yuv/hsv
# upscaler                = 2 # 1/2/4/8
# upscaler_method         = 'lanczos' # Lanczos/bicubic/ecbsr
# downscaler_method       = 'bilinear' # bilinear/bicubic/nearest
# sharpen_method          = 'wandcas' # cv1/wandcas/cvunet/sr
# suppress_color_noise    = 'none' # none/
# blending_amount         = 'all_100' # all_100/face/segmentation/blurbg
# output_directory        = '/home/<USER>/data/sharpen/testdataset'
# model_network           = 'UnetV3' # UnetV2/UnetV3
# model_path              = "/home/<USER>/modelhub/sharpen/SharpenV3_UnetV2_Epoch199_Loss0.0006.pth"
# patch_size              = 256    # 128/256/512/1024


def get_model(tag):
    model_tag = tag
    if model_tag == "UnetV2":
        return UNetV2()
    elif model_tag == "UnetV3":
        return UNetV3()
    else:
        raise ValueError("Invalid model tag {model_tag}")

def sharpen_pipeline(allparams):
    param = allparams["pipeline_control"]

    # list all images, 检查文件是否为图片（这里以.jpg为例，根据需要可以扩展其他格式）
    imagepaths = []
    for filename in os.listdir(param["input_directory"]):
        if filename.endswith(".jpg") or filename.endswith(".png"):
            filepath = os.path.join(param["input_directory"], filename)
            imagepaths.append(filepath)

    # create output directory if not exists
    output_directory = param["output_directory"]
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)

    # load model
    sharpen_method = param["sharpen_method"]
    if sharpen_method[0:2] == 'dl':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        inference_model = get_model(allparams["sharpen_method_dlunet"]["model_network"]).to(device)
        model_weights   = torch.load(allparams["sharpen_method_dlunet"]["model_path"], map_location=torch.device(device))   
        inference_model.load_state_dict(model_weights)
        inference_model.eval()
        allparams["sharpen_method_dlunet"]['inference_model'] = inference_model


    # process each image
    for imagepath in imagepaths:
        image_rgb_original = cv2.imread(imagepath)

        # color space convert , rgb/yuv/hsv
        color_space = param["color_space"]
        if color_space == 'yuv':
            image_yuv_original = cv2.cvtColor(image_rgb_original, cv2.COLOR_BGR2YUV)
            image_in = image_yuv_original[:, :, 0].copy()
        elif color_space == 'hsv':
            image_hsv_original = cv2.cvtColor(image_rgb_original, cv2.COLOR_BGR2HSV)
            image_in = image_hsv_original[:, :, 0].copy()
        elif color_space == 'rgb':
            image_in = image_rgb_original.copy()
        else:
            print('Error: unsupported color space: {color_space}')

        # upscaler_method , Lanczos/bicubic/sr 
        upscaler = param["upscaler"]
        upscaler_method = param["upscaler_method"]
        original_height, original_width, channels = image_rgb_original.shape
        if upscaler_method == 'lanczos':
            image = cv2.resize(image_in, None, fx=upscaler, fy=upscaler, interpolation=cv2.INTER_LANCZOS4)
        elif upscaler_method == 'bicubic':
            image = cv2.resize(image_in, None, fx=upscaler, fy=upscaler, interpolation=cv2.INTER_CUBIC)
        elif upscaler_method =='ecbsr':
            pass
        else:
            print('Error: unsupported upscaler: {upscaler}')

        # sharpen method
        if sharpen_method == 'cv1usm':
            image = cv1usm_sharpen(image, allparams)
        elif sharpen_method == 'cv2wandcas':
            image = cv2wandcas_sharpen(image, allparams)
        elif sharpen_method == 'dlunetv2':
            image = dlunet_sharpen(image, device, allparams)
        else:   
            print('Error: unsupported sharpen method: {sharpen}')

        # downscaler , bilinear/bicubic/nearest
        downscaler_method = param["downscaler_method"]
        if downscaler_method == 'bilinear':
            image = cv2.resize(image, (original_width, original_height), interpolation=cv2.INTER_LINEAR)
        elif downscaler_method == 'bicubic':
            image = cv2.resize(image, (original_width, original_height), interpolation=cv2.INTER_CUBIC)
        elif downscaler_method == 'nearest':
            image = cv2.resize(image, (original_width, original_height), interpolation=cv2.INTER_NEAREST)
        else:
            print('Error: unsupported downscaler: {downscaler}')


        # suppress color noise , none/
        suppress_color_noise = param["suppress_color_noise"]
        if suppress_color_noise== 'none':
            pass
        else:
            print('Error: unsupported suppress color noise method: {suppress_color_noise}')



        # control amount , all/face/segmentation/blurbg
        blending_amount = param["blending_amount"]
        if blending_amount.split('_')[0] == 'all':
            control_amount = float(blending_amount.split('_')[1])
            image = cv2.addWeighted(image_in, (100 - control_amount) / 100, image, control_amount / 100, 0)
        elif blending_amount == 'face':
            pass
        elif blending_amount =='segmentation':    
            pass
        elif blending_amount == 'blurbg':
            pass
        else:
            print('Error: unsupported control amount method: {blending_amount}')



        # color space convert
        color_space = param["color_space"]
        if color_space == 'yuv':
            image_yuv_original[:, :, 0] = image.copy()
            image_rgb_out = cv2.cvtColor(image_yuv_original, cv2.COLOR_YUV2BGR)
        elif color_space == 'hsv':
            image_hsv_original[:, :, 0] = image.copy()
            image_rgb_out = cv2.cvtColor(image_hsv_original, cv2.COLOR_HSV2BGR)
        elif color_space == 'rgb':
            image_rgb_out = image.copy()
        else:
            print('Error: unsupported color space: {color_space}')

        # save image
        filename        = os.path.basename(imagepath)
        ext             = filename.split('.')[-1]
        imagename       = filename.split('.')[0]
        new_filename    = imagename + f'-sharpened-{color_space}-{upscaler}-{sharpen_method+param["extra_tag"]}-{suppress_color_noise}-{blending_amount}.' + ext  
        print(f'saving {os.path.join(output_directory, new_filename)}')
        cv2.imwrite(os.path.join(output_directory, filename), image_rgb_original)   
        cv2.imwrite(os.path.join(output_directory, new_filename), image_rgb_out) 


def cv1usm_sharpen(image, allparams):
    # image_float = np.float32(image)
    image_float = image
    blurred     = image_float.copy()
    
    # 应用高斯模糊
    # 这里的(5, 5)是高斯核的大小，sigmaX可以设置为0让OpenCV自动从核大小计算
    param = allparams["sharpen_method_cv1sum"]
    if param["blur_filter"] == "bilateralfilter":
        blurred = cv2.bilateralFilter(image_float, param["blur_radius"], param["blur_sigmaColor"], param["blur_sigmaSpace"])
    elif param["blur_filter"] == "gaussianblur":
        blurred = cv2.GaussianBlur(image_float, (param["blur_radius"], param["blur_radius"]), param["blur_sigmaSpace"])
    else:      
        print('Error: unsupported blur filter: {param["blur_filter"]}')
    # wlsfilter = cv2.ximgproc.createFastGlobalSmootherFilter(image_float.shape[1], image_float.shape[0], 5, 5)
    # wlsfilter.apply(image_float, bilateralFilter, blurred)
    # blurred = cv2.GaussianBlur(image_float, (5, 5), 0)
    
    # 将原始图像与模糊图像相减得到锐化效果
    # 可以调整alpha, beta, gamma的值来控制锐化强度和效果
    alpha = param["amount"]
    usm_sharpened = cv2.addWeighted(image_float, 1 + alpha, blurred, -1 * alpha, 0)
    
    # 将结果转换回原图像类型
    usm_sharpened = np.uint8(usm_sharpened)
    return usm_sharpened  


def cv2wandcas_sharpen(image_in, allparams):
    param = allparams["sharpen_method_cv2wandcas"]

    # wand sharpen
    # numpy to wand
    image_wand = Image.from_array(image_in)
    # wand sharpen
    image_wand.auto_level()
    image_wand.sharpen(radius = param['wandradius'], sigma = param["wandsigma"])
    # wand to numpy
    image_wand = np.array(image_wand)

    # load wand sharpen result in torch & matpilotlib
    cas_sharpen = image_wand.astype(np.float32) / 255.0
    cas_sharpen = torch.from_numpy(cas_sharpen.copy())
    cas_sharpen = cas_sharpen.permute(-1, 0, 1)

    # apply cas sharpen, convert to 0 to 255
    wandcas_sharpen = contrast_adaptive_sharpening(cas_sharpen, param["casamount"]).permute(1, 2, 0).numpy()
    wandcas_sharpen = (wandcas_sharpen * 255).clip(0, 255).astype(np.uint8)

    return wandcas_sharpen

def dlunet_sharpen(image_in, device, allparams):
    param = allparams["sharpen_method_dlunet"]
    patch_size = param['patch_size']
    inference_model = param['inference_model']

    # numpy to tensor
    input_image  = torch.from_numpy(image_in.copy())
    input_image  = input_image.float() / 255.0
    input_image  = input_image.permute(2, 0, 1)
    input_image  = input_image.unsqueeze(0) # add batch dimension

    # to cuda
    input_image = input_image.to(device)
    print(input_image.shape)

    # 前向传播
    sharpen_image = input_image.clone()
    hindex = 2
    windex = 3
    # Iterate through the image and save tiles
    for i in range(0, input_image.shape[hindex], patch_size):
        for j in range(0, input_image.shape[windex], patch_size):
            input_tile      = input_image[:, :, i:i + patch_size, j:j + patch_size]
            # print(f"inference tile ({i}, {j}), {input_tile.shape}")

            with torch.no_grad():
                output_tile = inference_model(input_tile)
            sharpen_image[:, :, i:i + patch_size, j:j + patch_size] = output_tile
        input_tile = input_image[:, :, i:i + patch_size, (input_image.shape[windex] - patch_size):(input_image.shape[windex])]
        with torch.no_grad():
            output_tile = inference_model(input_tile)
        sharpen_image[:, :, i:i + patch_size, (input_image.shape[windex] - patch_size):(input_image.shape[windex])] = output_tile
    input_tile = input_image[:, :, (input_image.shape[hindex] - patch_size):(input_image.shape[hindex]), 
                                    (input_image.shape[windex] - patch_size):(input_image.shape[windex])]
    with torch.no_grad():
        output_tile = inference_model(input_tile)
    sharpen_image[:, :, (input_image.shape[hindex] - patch_size):(input_image.shape[hindex]), 
                        (input_image.shape[windex] - patch_size):(input_image.shape[windex])] = output_tile

    # log
    dump_output = sharpen_image[0].detach().cpu().numpy().transpose(1, 2, 0)
    cvunet_sharpen_output = (dump_output * 255.0).clip(0, 255).astype(np.uint8)
    return cvunet_sharpen_output



if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Sharpen pipeline')
    parser.add_argument('--config', type=str, default=None, help='config file path (default: None)')
    args = parser.parse_args()
    if args.config:
       yaml_args = yaml.load(open(args.config), Loader=yaml.FullLoader)
    else:
        yaml_args = yaml.load(open("/home/<USER>/code/IEDemo/algorithms/sharpen/config_v1.yml"), Loader=yaml.FullLoader)

    sharpen_pipeline(yaml_args)  
