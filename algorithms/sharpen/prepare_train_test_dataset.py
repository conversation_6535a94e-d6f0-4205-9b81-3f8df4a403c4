from PIL import Image
import os
import numpy as np


def tile_image(input_image_path, gt_image_path, tile_directory_input, tile_directory_sharp):

    # Create output directory if it doesn't exist
    if not os.path.exists(tile_directory_input):
        os.makedirs(tile_directory_input)
    if not os.path.exists(tile_directory_sharp):
        os.makedirs(tile_directory_sharp)

    # Open the image
    img0 = Image.open(input_image_path)
    input_img = np.array(img0)
    img1 = Image.open(gt_image_path)
    img_sharpen = np.array(img1)

    # Iterate through the image and save tiles
    for i in range(0, input_img.shape[0], patch_size):
        for j in range(0, input_img.shape[1], patch_size):
            print(f"Processing tile ({i}, {j})")

            input_tile      = input_img[i:i + patch_size, j:j + patch_size, :]
            sharpen_tile    = img_sharpen[i:i + patch_size, j:j + patch_size, :]

            # Check if the tile size is correct (256x256)
            if input_tile.shape == (patch_size, patch_size, 3):
                tile_filename = f'{os.path.splitext(os.path.basename(input_image_path))[0]}_tile_{i}_{j}.png'
                # Save input tile
                input_tile_path = os.path.join(tile_directory_input, tile_filename)
                input_tile = Image.fromarray(input_tile)
                input_tile.save(input_tile_path)
                # save sharpen tile
                sharpen_tile_path = os.path.join(tile_directory_sharp, tile_filename)
                sharpen_tile = Image.fromarray(sharpen_tile)
                sharpen_tile.save(sharpen_tile_path)


if __name__ == "__main__":
    # Define patch size
    patch_size = 256

    # Input directory containing images
    input_directory = "/home/<USER>/project/sharpen/input"

    # sharpen V3
    gt_directory     = "/home/<USER>/project/sharpen/gt50big"
    gt_tag           = '-topazsharpen50-sharpen.png'
    output_directory = "/home/<USER>/project/sharpen/traindatasetv4-gt50big"

    # Output directory for sharp tiles
    if not os.path.exists(output_directory): os.mkdir(output_directory)
    tile_directory_input = os.path.join(output_directory, "input")
    tile_directory_sharp = os.path.join(output_directory, "sharp")

    # Get the list of image files in the input directory
    image_files = [f for f in os.listdir(input_directory) if f.endswith('.png')]

    # Process each image file
    for image_file in image_files:
        input_image_path = os.path.join(input_directory, image_file)
        gt_image_name    = os.path.splitext(image_file)[0] + gt_tag
        gt_image_path    = os.path.join(gt_directory, gt_image_name)
        print(f"Processing image {image_file}")

        # Split the image into tiles (sharp)
        tile_image(input_image_path, gt_image_path, tile_directory_input, tile_directory_sharp)

    print("Tiles created successfully.")
