# import Image from wand.image module 
from wand.image import Image 
from skimage.filters import unsharp_mask
import skimage
import cv2, PIL
import os, torch
import numpy as np
import torch as th
import torch.nn.functional as F
import matplotlib.pyplot as plt
from cas import contrast_adaptive_sharpening
from PIL import ImageEnhance

 
# param.radius = 5
# param.sigma = 2
def wand_sharpen(image_path, param, output_folder_path):
    image_name = image_path.split("\\")[-1]
    [image_name_no_ext, image_ext] = image_name.split(".")

    with Image(filename = image_path) as img: 
        ori_width = img.width
        ori_height = img.height
        scale = param["upscale"]
        img.resize(ori_width * scale, ori_height * scale)
        img.sharpen(radius = param['wandradius'], sigma = param["wandsigma"]) 
        img.resize(ori_width, ori_height)

        output_name = f"{image_name_no_ext}_upscale{param["upscale"]}_wand{param['wandradius']}+{param["wandsigma"]}.{image_ext}"
        output_path = os.path.join(output_folder_path, output_name)
        img.save(filename = output_path)
        return img


# param.radius = 5
# param.amount = 2
def usm_sharpen(image_path, param, output_folder_path):
    image_name = image_path.split("\\")[-1]
    [image_name_no_ext, image_ext] = image_name.split(".")

    image = cv2.imread(image_path)

    if param["usm"] == 1:
        blur=cv2.medianBlur(image, param["usmradius"])
    elif param["usm"] == 2:
        blur=cv2.GaussianBlur(image,(0,0), param["usmradius"])
    else:
        blur=cv2.bilateralFilter(image, param["usmradius"], 128, 50)
    # sharpeing of image by blending 
    hp1 = cv2.addWeighted(image, 10, blur, -10, 0)
    hp2 = cv2.addWeighted(image, -10, blur, 10, 0)
    result1 = cv2.addWeighted(image, 1.0, hp1, param["usmamount"] / 10.0, 0)
    result = cv2.addWeighted(result1, 1.0, hp2, -param["usmamount"] / 10.0, 0)

    output_name = f"{image_name_no_ext}_usm{param["usm"]}+{param["usmradius"]}+{param["usmamount"]}.{image_ext}"
    output_path = os.path.join(output_folder_path, output_name)

    cv2.imwrite(output_path, result)
    return result


# https://github.com/Jamy-L/Pytorch-Contrast-Adaptive-Sharpening
# param.amount = 0.8
def cas_sharpen(image_path, param, output_folder_path):
    image_name = image_path.split("\\")[-1]
    [image_name_no_ext, image_ext] = image_name.split(".")

    im = torch.from_numpy(plt.imread(image_path).copy())
    im = im.permute(-1, 0, 1)

    output_name = f"{image_name_no_ext}_cas{param["casamount"]}.{image_ext}"
    output_path = os.path.join(output_folder_path, output_name)
    result = contrast_adaptive_sharpening(im, param["casamount"]).permute(1, 2, 0).numpy()
    plt.imsave(output_path, result)
    return result


# Contrast Enhancement
def CLAHE(I):
    lab = cv2.cvtColor(I, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
    l = clahe.apply(l)
    O = cv2.merge([l, a, b])
    O = cv2.cvtColor(O, cv2.COLOR_LAB2BGR)
    return O
    
def sharpen_USM_C(I, param):
    row, col, dem = I.shape
    sigma = param["clahesigma"]
    kernel_size = (0, 0)
    C = CLAHE(I)
    L = cv2.GaussianBlur(I, kernel_size, sigma)
    L = L.astype(np.int32)
    I = I.astype(np.int32)
    H = I - L
    H[H > 255] = 255
    H[H < 0] = 0
    lab_H = cv2.cvtColor(H.astype(np.uint8), cv2.COLOR_BGR2LAB)
    l_H = cv2.split(lab_H)[0]
    threshold = 1
    O = I.copy()
    for i in range(row):
        for j in range(col):
            for k in range(dem):
                percent = param["clahefactor"] * l_H[i, j] / 100
                diff = C[i, j, k] - I[i, j, k]
                delta = percent * diff
                if abs(delta) >= threshold:
                    O[i, j, k] += delta
    O[O > 255] = 255
    O[O < 0] = 0
    O = O.astype(np.uint8)
    return O


def clahe_sharpen(image_path, param, output_folder_path):
    image_name = image_path.split("\\")[-1]
    [image_name_no_ext, image_ext] = image_name.split(".")

    image = cv2.imread(image_path)
    result = sharpen_USM_C(image, param)

    output_name = f"{image_name_no_ext}_clahe{param["clahesigma"]}+{param["clahefactor"]}.{image_ext}"
    output_path = os.path.join(output_folder_path, output_name)
    cv2.imwrite(output_path, result)
    return result




def wand_cas_sharpen(image_path, param, output_folder_path):
    image_name = image_path.split("\\")[-1]
    [image_name_no_ext, image_ext] = image_name.split(".")

    imagein_name = f"{image_name_no_ext}_upscale{param["upscale"]}_wand{param['wandradius']}+{param["wandsigma"]}.{image_ext}"
    output_path = os.path.join(output_folder_path, imagein_name)
    result = cas_sharpen(output_path, param, output_folder_path)
    output_name = f"{image_name_no_ext}_upscale{param["upscale"]}_wandcas.{image_ext}"
    output_path = os.path.join(output_folder_path, output_name)
    cv2.imwrite(output_path, result)


def generate_cvsharpen_gt(image_path, param, output_folder_path=None):
    imagein = Image(filename = image_path)
    origin_width = imagein.width
    origin_height = imagein.height

    # wand sharpen
    scale = param["upscale"]
    imagein.resize(origin_width * scale, origin_height * scale)
    imagein.sharpen(radius = param['wandradius'], sigma = param["wandsigma"]) 
    imagein.resize(origin_width, origin_height)

    # save wand result
    image_name = image_path.split("\\")[-1]
    [image_name_no_ext, image_ext] = image_name.split(".")
    output_name = f"wandsharpen.{image_ext}"
    imagein.save(filename = output_name)

    # load wand sharpen result in torch & matpilotlib
    wand_sharpen = cv2.imread(output_name) / 255.0
    # wand_sharpen = cv2.cvtColor(wand_sharpen, cv2.COLOR_BGR2RGB)
    im = torch.from_numpy(wand_sharpen.copy())
    im = im.permute(-1, 0, 1)

    # apply cas sharpen
    wandcas_sharpen = contrast_adaptive_sharpening(im, param["casamount"]).permute(1, 2, 0).numpy()
    
    # save 
    if output_folder_path:
        wand_output_name       = f"{image_name_no_ext}_wand.{image_ext}"
        wandcas_output_name    = f"{image_name_no_ext}.{image_ext}"
        wand_output_path = os.path.join(output_folder_path, wand_output_name)
        wandcas_output_path = os.path.join(output_folder_path, wandcas_output_name)
        # wand_sharpen = cv2.cvtColor(wand_sharpen, cv2.COLOR_RGB2BGR)
        # wandcas_sharpen = cv2.cvtColor(wandcas_sharpen, cv2.COLOR_RGB2BGR)
        # cv2.imwrite(wand_output_path, wand_sharpen * 255)
        cv2.imwrite(wandcas_output_path, wandcas_sharpen * 255)

    return wand_sharpen, wandcas_sharpen



if __name__ == "__main__":
    images_folder_path = "D:\\data\\dd_dp_dataset_canon\\dd_dp_dataset_png\\train_c\\target"
    
    # sharpenV1
    param = {"upscale":2, "wandradius":5, "wandsigma":2, "usm": 3, "usmradius":5, "usmamount":1.5, "casamount":0.5, "clahesigma":3, "clahefactor":1.5}
    output_folder_path = "D:\\data\\dd_dp_dataset_canon\\dd_dp_dataset_png\\train_c\\target\\sharpen"
    # sharpenV2
    param = {"upscale":2, "wandradius":4, "wandsigma":3, "usm": 3, "usmradius":5, "usmamount":1.5, "casamount":0.8, "clahesigma":3, "clahefactor":1.5}
    output_folder_path = "D:\\data\\dd_dp_dataset_canon\\dd_dp_dataset_png\\train_c\\target\\sharpenV2"


    # find all images 
    image_list = os.listdir(images_folder_path)


    # sharpen for images 
    for image_name in image_list:
        image_path = os.path.join(images_folder_path, image_name)
        print(image_name)
        
        # sharpen
        # wandsharpen = wand_sharpen(image_path, param, output_folder_path)
        # usmsharpen = usm_sharpen(image_path, param, output_folder_path)
        # cassharpen = cas_sharpen(image_path, param, output_folder_path)
        # clahesharpen = clahe_sharpen(image_path, param, output_folder_path)
        # wand_cas_sharpen(image_path, param, output_folder_path)
        if not os.path.exists(output_folder_path): os.mkdir(output_folder_path)
        generate_cvsharpen_gt(image_path, param, output_folder_path)
