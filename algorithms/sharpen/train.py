

import sys
import torch, cv2, os, random
from torch import nn
import torch.nn.functional as F
from torchvision import transforms
import numpy as np
from torch.utils.data import DataLoader
from networks import UNetV2, UNetV3
from datasetloader import   Sharpen_Dataset



def resume_checkpoint(model, resume_point, optimizer):  
    last_epoch= 0 
    if resume_point is not None:
        pretrained_model  = torch.load(resume_point)
        model.load_state_dict(pretrained_model)
        last_epoch = int(resume_point.split("_")[1][5:])
    return last_epoch


class CombinedLoss(nn.Module):
    def __init__(self, alpha=0.5, beta=0.5):
        super(CombinedLoss, self).__init__()
        self.alpha = alpha 
        self.beta = beta
        self.gaussian_filter = self.create_gaussian_filter()
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)

        # 将Sobel算子扩展到三个通道（RGB）
        sobel_x = torch.stack([sobel_x] * 3, dim=0)
        sobel_y = torch.stack([sobel_y] * 3, dim=0)

        # 将Sobel算子的形状调整为 [out_channels, in_channels, kernel_height, kernel_width]
        self.sobel_x = sobel_x.unsqueeze(0).cuda()  # [1, 3, 3, 3]
        self.sobel_y = sobel_y.unsqueeze(0).cuda()  # [1, 3, 3, 3]

    def create_gaussian_filter(self, kernel_size=21):
        # 计算高斯核
        sigma = kernel_size / 6
        range = torch.arange(-kernel_size // 2 + 1, kernel_size // 2 + 1, dtype=torch.float32)
        x, y = torch.meshgrid(range, range)
        kernel = torch.exp(-(x**2 + y**2) / (2 * sigma**2))
        kernel /= kernel.sum()
        return kernel.cuda()

    def forward(self, pred, target):
        # MSE Loss
        mse_loss = F.mse_loss(pred, target)

        # Sobel Loss
        output_x = F.conv2d(pred.float(), self.sobel_x, padding=1) - F.conv2d(target.float(), self.sobel_x, padding=1)
        output_y = F.conv2d(pred.float(), self.sobel_y, padding=1) - F.conv2d(target.float(), self.sobel_y, padding=1)
        sobel_loss = torch.mean(output_x**2 + output_y**2)

        # Color Loss
        gaussian_filter = torch.stack([self.gaussian_filter] * 3, dim=0)
        gaussian_filter = gaussian_filter.unsqueeze(0) 
        pred_filtered   = F.conv2d(pred, gaussian_filter, padding=gaussian_filter.size(2) // 2)
        target_filtered = F.conv2d(target, gaussian_filter, padding=gaussian_filter.size(2) // 2)
        color_loss = F.mse_loss(pred_filtered, target_filtered)

        # 计算总损失
        total_loss = mse_loss + self.alpha * sobel_loss + self.beta * color_loss
        return total_loss


def get_model(tag):
    model_tag = tag.split("_")[1]
    if model_tag == "UnetV2":
        return UNetV2()
    elif model_tag == "UnetV3":
        return UNetV3()
    else:
        raise ValueError("Invalid model tag {model_tag}")

def main(tag, input_image_path, sharp_image_path, resumepoint):

    # unet
    model = get_model(tag).cuda() 

    # network init 
    loss_fn = CombinedLoss(alpha=0.5, beta=0.5)  # 实例化自定义损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)

    # 定义图像和文本的输入，定义dataloader
    dataset = Sharpen_Dataset(input_image_path, sharp_image_path)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers)

    last_epoch = resume_checkpoint(model, resumepoint, optimizer)

    # 训练模型
    model.train()
    # 训练循环
    for epoch in range(epoch_max):  
        #for image, text in dataloader:
        for batch_num, input_datain in enumerate(dataloader):
                    
            # input, gt
            input_image  = input_datain[0]
            gt_image     = input_datain[1]

            # 应用图像转换
            input_image = input_image.cuda()
            gt_image = gt_image.cuda()

            # 前向传播
            output_image = model(input_image)

            # 计算损失
            loss = loss_fn(output_image, gt_image)
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            print(".", end="")
            sys.stdout.flush()

        # log
        index  = np.random.randint(0, len(input_image)-1)
        dump_input = input_image[index].detach().cpu().numpy().transpose(1, 2, 0)
        dump_gt = gt_image[index].detach().cpu().numpy().transpose(1, 2, 0)
        dump_output = output_image[index].detach().cpu().numpy().transpose(1, 2, 0)
        cv2.imwrite(os.path.join(dump_path, f'Epoch{last_epoch + epoch}_Input.png'), dump_input * 255)
        cv2.imwrite(os.path.join(dump_path, f'Epoch{last_epoch + epoch}_GT.png'), dump_gt * 255)
        cv2.imwrite(os.path.join(dump_path, f'Epoch{last_epoch + epoch}_Output.png'), dump_output * 255)
        print("dump to path : {dump_path} ")
        
        print(f'Epoch: {last_epoch + epoch}, Loss: {loss.item()}')
        if (last_epoch + epoch) % save_pth_interval == 0:
            torch.save(model.state_dict(), os.path.join(model_path, f"{tag}_Epoch{last_epoch + epoch}_Loss{round(loss.item(), 4)}.pth"))

    torch.save(model.state_dict(), os.path.join(model_path, f"{tag}_Epoch{last_epoch + epoch}_Loss{round(loss.item(), 4)}.pth"))


if __name__ == "__main__":
    # fix seed 
    seed = 42
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.enabled = False

    batch_size  = 16
    num_workers = 4
    epoch_max   = 200
    lr          = 5e-5
    save_pth_interval = 10

    # model output 
    model_path = "/home/<USER>/modelhub/sharpen"

    # projects

    tag = "SharpenV5_UnetV2"
    resume_point = None
    input_image_path     = "/home/<USER>/project/sharpen/traindatasetv4-gt50big/input"
    sharp_image_path     = "/home/<USER>/project/sharpen/traindatasetv4-gt50big/sharp"
    dump_path            = "/home/<USER>/project/sharpen/dump" + tag

    # tag = "SharpenV4_UnetV2"
    # resume_point = None
    # input_image_path     = "/home/<USER>/project/sharpen/traindatasetv3-gt50/input"
    # sharp_image_path     = "/home/<USER>/project/sharpen/traindatasetv3-gt50/sharp"
    # dump_path            = "/home/<USER>/project/sharpen/dump" + tag

    # tag = "SharpenV3_UnetV2_gt100"
    # resume_point = None
    # input_image_path     = "/home/<USER>/project/sharpen/traindatasetv3-gt100/input"
    # sharp_image_path     = "/home/<USER>/project/sharpen/traindatasetv3-gt100/sharp"
    # dump_path            = "/home/<USER>/project/sharpen/dump" + tag
    

    print(tag)
    print(input_image_path)
    print(sharp_image_path)
    print(dump_path)
    if not os.path.exists(dump_path): os.mkdir(dump_path)
    if not os.path.exists(model_path): os.mkdir(model_path)
    main(tag, input_image_path, sharp_image_path, resume_point)