name: PyLint

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8]

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install codespell flake8 isort yapf

    - name: Lint
      run: |
        codespell
        flake8 .
        isort --check-only --diff basicsr/ options/ scripts/ tests/ inference/ setup.py
        yapf -r -d basicsr/ options/ scripts/ tests/ inference/ setup.py
