{
    "files.trimTrailingWhitespace": true,
    "editor.wordWrap": "on",
    "editor.rulers": [
        80,
        120
    ],
    "editor.renderWhitespace": "all",
    "editor.renderControlCharacters": true,
    "python.formatting.provider": "yapf",
    "python.formatting.yapfArgs": [
        "--style",
        "{BASED_ON_STYLE = pep8, BLANK_LINE_BEFORE_NESTED_CLASS_OR_DEF = true, SPLIT_BEFORE_EXPRESSION_AFTER_OPENING_PAREN = true, COLUMN_LIMIT = 120}"
    ],
    "python.linting.flake8Enabled": true,
    "python.linting.flake8Args": [
        "max-line-length=120"
    ],
}
